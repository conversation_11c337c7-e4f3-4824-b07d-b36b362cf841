name: schnell_pole_installation
description: A new Flutter project.

publish_to: 'none'

version: 2.2.2+1

environment:
  sdk: '>=3.2.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2
  dio: ^5.2.1+1
  path: ^1.8.2
  quickalert: ^1.1.0
  camera: ^0.10.5
  geocoding: ^2.0.5
  geolocator: ^13.0.1
  google_fonts: ^4.0.4
  barcode_scan2: ^4.2.1
  toggle_switch: ^2.0.1
  path_provider: ^2.0.11
  flutter_spinkit: ^5.1.0
  connectivity_plus: ^6.1.3
  flutter_easyloading: ^3.0.5
  permission_handler: ^11.4.0
  shared_preferences: ^2.0.15
  lottie:
  app_settings: ^5.0.0
  receive_intent: ^0.2.4
  another_flushbar: ^1.12.30
  url_launcher:
  provider: ^6.0.5
  sqflite: ^2.2.8+4
  in_app_update: ^4.1.4
  animated_hint_textfield: ^1.0.1
  dropdown_button2: ^2.3.9
  uuid: ^4.4.2
  flutter_riverpod: ^2.5.1
  image: ^4.2.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  open_file: ^3.5.10
  syncfusion_flutter_xlsio: ^29.2.11
  device_info_plus: ^11.4.0
  flutter_internet_signal: ^0.0.30
  carrier_info: ^2.0.8
  aws_s3_upload:
  crypto: ^3.0.3

dependency_overrides:
  permission_handler_android: ^13.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0

flutter:

  uses-material-design: true

  assets:
    - assets/images/
    - assets/animation/
