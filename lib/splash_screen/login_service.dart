import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:schnell_pole_installation/utils/dio_client.dart';
import '../env.dart';

class LoginService {
  var token;
  var refreshToken;
  var status;
  var message;

  // Future<String> loginCheck(context) async {
  //   var adata = FormData.fromMap({
  //     "username": "<EMAIL>",
  //     "password": "schnell@123",
  //     "app": "luminator",
  //     "version": appVersion
  //   });
  //   Dio dio = Dio();
  //   try {
  //     Response response = await dio.post('$baseUrl/api/login/',
  //         data: adata,
  //         options: Options(contentType: 'application/json', headers: {
  //           "Accept": "application/json",
  //         }));
  //     if (response.data != "") {
  //       var data = jsonDecode(response.data);
  //       status = data["status"];
  //       message = data['message'];
  //       token = data['token'];
  //       refreshToken = data['refreshToken'];
  //     } else {
  //       ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
  //         content: Text('No Data Found'),
  //         duration: Duration(seconds: 1),
  //       ));
  //     }
  //     return token;
  //   } catch (e) {
  //     if (e is DioException) {
  //       EasyLoading.dismiss(animation: true);
  //       return alertPopUp(context, 'Server Timeout Please Try Again!',
  //           'assets/animation/warn.json');
  //     } else {
  //       EasyLoading.dismiss(animation: true);
  //       return alertPopUp(context, 'Something went wrong. Please try Again!',
  //           'assets/animation/warn.json');
  //     }
  //   }
  // }

  Future<String> poleCount(token1, context, wardId) async {
    Dio dio = DioClient.dio;
    try {
      Response response = await dio.get(
        '$baseUrl/api/get/entity/count/?wardId=$wardId&assetType=pole',
        options: Options(headers: {
          // "token": token1,
        }),
      );
      var data = jsonDecode(response.data);
      log('pole count $data');
      var poleCount = data['pole_count'].toString();
      return poleCount;
    } catch (e) {
      if (e is DioException) {
        log('pole count dio error $e');

        EasyLoading.dismiss(animation: true);
        if (e.error == 'Session expired. Please login again.') {
          return '401';
        } else {
          return "error!!";
        }
      } else {
        EasyLoading.dismiss(animation: true);
        return "Something went wrong. Please try Again!";
      }
    }
  }

  Future<dynamic> dayCount(token1, context, wardId, user) async {
    Dio dio = DioClient.dio;
    try {
      Response response = await dio.get(
        '$baseUrl/api/get/user/installation/count/?user=$user&assetType=pole&wardId=$wardId',
        options: Options(headers: {
          // "token": token1,
        }),
      );
      Map<String, dynamic> data = jsonDecode(response.data);
      log('specific user count $data');

      // Map<String, dynamic> data = {'status' : 401};
      return data;
    } catch (e) {
      if (e is DioException) {
        EasyLoading.dismiss(animation: true);
        if (e.error == 'Session expired. Please login again.') {
          return '401';
        } else {
          return "error!!";
        }
      } else {
        EasyLoading.dismiss(animation: true);
        return "Something went wrong. Please try Again!";
      }
    }
  }
}
