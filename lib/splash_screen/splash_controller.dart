import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/pole_installation.dart';

class SplashController {
  loginCheck(context, token, region, zone, ward, wardId, userName, customerId,
      poleNoTemplate, wardNumberForPolePrefix) {
    Timer timer = Timer(const Duration(milliseconds: 2000), () async {
      var dataBloc = Provider.of<DataModel>(context, listen: false);
      dataBloc.token = token;
      dataBloc.region = region;
      dataBloc.zone = zone;
      dataBloc.ward = ward;
      dataBloc.wardId = wardId;
      dataBloc.userName = userName;
      dataBloc.customerId = customerId;
      dataBloc.poleNoTemplate = poleNoTemplate;
      dataBloc.wardNumberForPolePrefix = wardNumberForPolePrefix;
      //  var result = await _service.poleCount(token, context, wardId);
      //  if(result != ""){
      // Navigator.pushNamed(context, dashboardRoute);
      Navigator.pushReplacement(context,
          MaterialPageRoute(builder: (context) => const PoleInstallation()));
      //  }
    });
  }
}
