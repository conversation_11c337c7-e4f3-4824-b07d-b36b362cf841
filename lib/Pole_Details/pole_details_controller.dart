import 'dart:convert';
import 'dart:developer';

import 'package:geolocator/geolocator.dart';
import 'package:schnell_pole_installation/DB/db_repo.dart';
import 'package:schnell_pole_installation/DB/map_model.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_details_service.dart';
import 'package:schnell_pole_installation/attendance_tracking.dart/user_tracking_service.dart';
import 'package:schnell_pole_installation/utils/utility.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PoleDetailController {
  final PoleDetailService _service = PoleDetailService();
  final UserTrackingService _userTrackingService = UserTrackingService();

  Future<String> validatePollInstallSucced(
      context, data, token, poleName) async {
    var result = await _service.pollInstallSucced(context, data);
    if (result == "200") {
      log('$poleName data uploaded successfully');
      final prefs = await SharedPreferences.getInstance();
      bool isLocationTrackingRequi =
          prefs.getBool('isLocationTrackingRquired') ?? false;

      if (isLocationTrackingRequi) {
        activityLocationTracking('Pole Installation', poleName);
      }
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_pole_db', poleName);
    } else if (result == '400' || result == '401') {
      var poleCountData = PoleCountModel();
      poleCountData.name = data['name'];
      poleCountData.type = data['type'];
      poleCountData.customerId = data['customerId'];
      poleCountData.latitude = data['latitude'];
      poleCountData.longitude = data['longitude'];
      poleCountData.accuracy = data['accuracy'];
      poleCountData.location = data['location'];
      poleCountData.state = data['state'];
      poleCountData.wardName = data['wardName'];
      poleCountData.zoneName = data['zoneName'];
      poleCountData.region = data['region'];
      poleCountData.installedOn = data['installedOn'];
      poleCountData.clampDimension = data['clampType'];
      if (data['armCount'] != 0) {
        poleCountData.lampProfiles = data['lampProfiles'];
      }
      // poleCountData.lampType = data['lampType'];
      // poleCountData.lampWattage = data['lampWattage'];
      poleCountData.connection = data['connection'];
      poleCountData.armCount = data['armCount'];
      poleCountData.installedBy = data['installedBy'];
      poleCountData.wardId = data['wardId'];
      var duplicateDbData = await DatabaseRepo()
          .insertDetailsData('tbl_duplicate_db', poleCountData);
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_pole_db', poleName);
      // alertPopUp(context,'Pole Already Exist','assets/animation/userAlert.json');
    }
    return result;
  }

  Future<void> activityLocationTracking(
    activityName,
    entityName,
  ) async {
    try {
      double latitude = 0.0;
      double longitude = 0.0;
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final position = await Geolocator.getCurrentPosition(
        locationSettings: AndroidSettings(accuracy: LocationAccuracy.high),
      );
      latitude = position.latitude;
      longitude = position.longitude;
      log("Location Tracking during activity: $latitude, $longitude at $timestamp");

      await storeLocationLocally(
        timestamp,
        lat: latitude,
        long: longitude,
        locationEnabled: true,
        activityName: activityName,
        entityName: entityName ?? '',
      );
      await Utility.isConnected().then((value) async {
        if (value) {
          //if internet available else location details just stored locally
          await _userTrackingService.pushLocationToUserTelemetry();
        }
      });
    } catch (e) {
      log("Location error: $e");
    }
  }

  Future<void> storeLocationLocally(
    timestamp, {
    double? lat,
    double? long,
    bool locationEnabled = true,
    String? activityName,
    String? entityName,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> locations = prefs.getStringList("locations") ?? [];
    Map<String, dynamic> values = {
      if (!locationEnabled) ...{
        "locationEnabled": false,
      } else ...{
        // Activity tracking
        "latitude": lat,
        "longitude": long,
        "activity": activityName,
        "entityName": entityName,
        "locationEnabled": true,
      }
    };
    locations.add(jsonEncode({"ts": timestamp, "values": values}));
    await prefs.setStringList("locations", locations);
    var storedLocations = prefs.getStringList("locations");
    log("Location saved locally $storedLocations");
  }

  Future<void> clearStoredLocations(location) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(location);
    log("Stored locations cleared");
  }
}
