import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:animated_hint_textfield/animated_hint_textfield.dart';
import 'package:app_settings/app_settings.dart';
import 'package:camera/camera.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/DB/db_repo.dart';
import 'package:schnell_pole_installation/DB/map_model.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_details_controller.dart';
import 'package:schnell_pole_installation/splash_screen/login_service.dart';
import 'package:schnell_pole_installation/splash_screen/splash_page.dart';
import 'package:schnell_pole_installation/take_photo/image_address_append.dart';
import 'package:schnell_pole_installation/take_photo/take_photo.dart';
import 'package:schnell_pole_installation/take_photo/take_photo_controller.dart';
import 'package:schnell_pole_installation/utils/box_container.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/dialog_box.dart';
import 'package:schnell_pole_installation/utils/utility.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../Provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../utils/loader.dart';

class PoleDetails extends StatefulWidget {
  const PoleDetails(
      {super.key,
      required this.region,
      required this.zone,
      required this.ward,
      required this.poleNumber,
      required this.customerId,
      required this.wardId,
      required this.installedOn,
      required this.installedBy,
      required this.token,
      required this.poleCount,
      required this.specificUserPoleCount});
  final String token;
  final String region;
  final String zone;
  final String ward;
  final String poleNumber;
  final String customerId;
  final String wardId;
  final String installedOn;
  final String installedBy;
  final String poleCount;
  final int specificUserPoleCount;

  @override
  State<PoleDetails> createState() => _PoleDetailsState();
}

class _PoleDetailsState extends State<PoleDetails> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  // final PoleDetailController _service = PoleDetailController();
  //  final PoleInstallationController _service = PoleInstallationController();
  late Timer sendDatatimer;
  late Timer sendImagetimer;
  final PoleDetailController detailService = PoleDetailController();
  final TakePhotoController imageService = TakePhotoController();
  final TextEditingController clampTypeLength = TextEditingController();
  final TextEditingController clampTypeWidth = TextEditingController();
  final TextEditingController locationAddress = TextEditingController();
  final LoginService service = LoginService();
  late List<ConnectivityResult> connectivityResult;
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();
  String poleCount = '';
  int specificUserPoleCount = 0;
  bool isLoading = true;
  bool locationLoading = false;
  String dropdowntypes = 'RCC-Normal';
  String dropdownarms = '1';
  String dropdownlampWatt = '40 W';
  String dropdownClampType = '6 x 3 inch';
  String dropdownUnits = 'inch';
  String customInputLength = '';
  String customInputWidth = '';
  String pollNumber = '';
  String dropdownconnection = '';
  String? subLocality;
  String latitude = '';
  String longitude = '';
  String accuracy = '';
  String? location;
  String? manualEnteredLocation;

  // int numberOfContainers = 1;
  // List<Map<String, String>> selectedItemValue = [];
  // String? selectedLampType = 'Tube light';
  // String? selectedLampWattage;
  // var lampWatt = {
  //   '40 W': 'Tube light',
  //   '150 W': 'SVL',
  //   '250 W': 'SVL',
  //   '400 W': 'SVL',
  //   '25 W': 'CFL',
  //   '36 W': 'CFL',
  //   '65 W': 'CFL',
  //   '72 W': 'CFL',
  //   '85 W': 'CFL',
  //   '20 W': 'LED',
  //   '40 W': 'LED',
  //   '60 W': 'LED',
  //   '70 W': 'LED',
  //   '90 W': 'LED',
  //   '120 W': 'LED',
  //   '150 W': 'LED',
  //   '200 W': 'LED',
  //   '96 W': 'T5',
  //   '120 W': 'T5',
  //   '220 W': 'T5',
  //   '40 W': 'T5',
  //   '10 W': 'BULB',
  //   '40 W': 'BULB',
  // };
  // List lampWatts = [];
  // WattDependentDropdown(lampTypeItems) {
  //   lampWatt.forEach((key, value) {
  //     if (lampTypeItems == value) {
  //       lampWatts.add(key);
  //     }
  //   });
  //   selectedLampType = lampWatts[0];
  // }

  List<Map<String, String>> selectedlamps = [
    {'type': 'Tube light', 'watts': '40 W'}
  ];

  List<String> items = ['40 W'];

  List<String> types = [
    'RCC-Normal',
    'RCC-Big',
    'Rail',
    'Tubular',
    'Octagonal',
  ];
  List<String> clampTypes = [
    'Clamp Type Not Required',
    '6 x 3 inch',
    '7 x 3 inch',
    '8 x 4 inch',
    '11 x 3 inch',
    '12 x 4 inch',
    'Others',
  ];
  List<String> units = ['cm', 'inch', 'mm'];
  List<String> arms = ['0', '1', '2', '3', '4', '6', '8', '12'];
  List<String> lamp = [
    'Tube light',
    'SVL',
    'CFL',
    'LED',
    'T5',
    'BULB',
  ];
  List<String> tubeLight = [
    '40 W',
  ];
  List<String> svlLamp = [
    '150 W',
    '250 W',
    '400 W',
    // '40 W',
  ];
  List<String> cflLamp = [
    '25 W',
    '36 W',
    '65 W',
    '72 W',
    '85 W',
    // '40 W',
  ];

  List<String> ledlLamp = [
    '20 W',
    '40 W',
    '60 W',
    '70 W',
    '90 W',
    '120 W',
    '150 W',
    '200 W',
  ];
  List<String> t5 = [
    '96 W',
    '120 W',
    '220 W',
    // '40 W',
  ];
  List<String> bulb = [
    '10 W',
    // '40 W',
  ];

  bool vertical = false;
  String toggleValue = 'OH';
  List<bool> selectedToggle = <bool>[true, false];
  bool hasInternet = false;
  List<Widget> toggle = <Widget>[
    const Text('Overhead'),
    const Text('Underground'),
  ];
  String state = 'INSTALLED';
  // late Timer timer;
  var todaysDate =
      '${DateTime.now().day.toString().length == 1 ? '0${DateTime.now().day.toString()}' : DateTime.now().day.toString()}-${DateTime.now().month.toString().length == 1 ? '0${DateTime.now().month.toString()}' : DateTime.now().month.toString()}-${DateTime.now().year.toString()}';
  @override
  void initState() {
    poleCount = widget.poleCount;
    specificUserPoleCount = widget.specificUserPoleCount;
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    initConnectivity();
    setAccuracy(); //modified
    pollNumber = widget.poleNumber;
    // checkInternet(context);
    // timer = Timer.periodic(const Duration(minutes: 5), (timer) {
    //   checkInternet(context);
    // });
    sendDatatimer = Timer.periodic(const Duration(minutes: 10), (value) {
      autoUploadData(context);
    });
    sendImagetimer = Timer.periodic(const Duration(minutes: 10), (value) {
      autoUploadImage(context);
    });
    super.initState();
  }

  @override
  void dispose() {
    // timer.cancel();
    _connectivitySubscription.cancel();
    sendDatatimer.cancel();
    sendImagetimer.cancel();
    super.dispose();
  }

  checkInternet(BuildContext context) {
    Utility.isConnected().then((value) {
      if (value) {
        setState(() {
          hasInternet = true;
        });
        return null;
      } else {
        setState(() {
          hasInternet = false;
        });
      }
    });
  }

  Future<void> initConnectivity() async {
    late List<ConnectivityResult> result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
      debugPrint('Connection Status : $result');
    } on PlatformException catch (e) {
      debugPrint(
        'Couldn\'t check connectivity status : $e',
      );
      return;
    }
    if (!mounted) {
      return Future.value(null);
    }

    return _updateConnectionStatus(result);
  }

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    setState(() {
      _connectionStatus = result;
    });
    debugPrint('Update Connection Status : $_connectionStatus');
    if (_connectionStatus.contains(ConnectivityResult.none) ||
        _connectionStatus.isEmpty) {
      setState(() {
        hasInternet = false;
      });
    } else {
      setState(() {
        hasInternet = true;
      });
    }
  }

  autoUploadData(BuildContext context) async {
    Utility.isConnected().then((value) async {
      if (value) {
        List datalength =
            await DatabaseRepo().readAllDataDetails('tbl_pole_db');
        if (datalength.isNotEmpty) {
          for (int i = 1; i <= datalength.length; i++) {
            var localresult =
                await DatabaseRepo().readOneDetailsData('tbl_pole_db');
            if (context.mounted) {
              await dataUpdation(localresult[0], context);
            }
          }
        }
      } else {
        null;
      }
    });
  }

  autoUploadImage(BuildContext context) async {
    Utility.isConnected().then((value) async {
      if (value) {
        List datalength =
            await DatabaseRepo().readAllDataDetails('tbl_image_db');
        if (datalength.isNotEmpty) {
          for (int i = 1; i <= datalength.length; i++) {
            var localDataResult =
                await DatabaseRepo().readOneDetailsData('tbl_pole_db');
            var localresult =
                await DatabaseRepo().readOneDetailsData('tbl_image_db');
            if (context.mounted) {
              await imageUpdation(localresult[0], localDataResult[0], context);
            }
          }
        }
      } else {
        null;
      }
    });
  }

  imageUpdation(imageData, localDataResult, BuildContext context) async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    ImageAddressAppender appender = ImageAddressAppender();
    String imageBase64 = imageData['deviceImage'];
    String locationCheck = imageData['location'] ?? '';
    String wtrmrkLocation = locationCheck == ''
        ? await getLocation(imageData['latitude'], imageData['longitude'])
        : locationCheck;
    Uint8List imageBytes = base64Decode(imageBase64);
// Get the directory to save the image
    Directory tempDir = await getTemporaryDirectory();
    String tempPath = tempDir.path;
    File imageFile =
        File('$tempPath/image.png'); //Create a file to write the image bytes
    await imageFile
        .writeAsBytes(imageBytes); //Write the image bytes to the file

    // Step 5: Return the file path
    // return imageFile.path;
    await appender.appendAddressToImage(
      imageFile.path,
      imageData['latitude'],
      imageData['longitude'],
      imageData['name'] ?? '',
      imageData['accuracy'],
      imageData['manualEnteredLocation'] ?? '',
      wtrmrkLocation,
      imageData['region'] ?? '',
      imageData['zoneName'] ?? '',
      imageData['wardName'] ?? '',
    );
    final bytes = File(imageFile.path).readAsBytesSync();
    String deviceImage = base64Encode(bytes);
    var result = await imageService.imageUpdation(
        deviceImage, dataBloc.token, imageData, context);
  }

  dataUpdation(value, BuildContext context) async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    var locationCheck = value['location'];
    var manualEnteredLocation = value['manualEnteredLocation'];
    String? filteredLandMark;
    if (locationCheck != null) {
      String? concatenatedLocation = '$manualEnteredLocation,$locationCheck';
      filteredLandMark = concatenatedLocation
          .replaceAll(RegExp(r',\s*'), ',')
          .replaceAll(RegExp(r'^,\s*'), '')
          .trim();
    }

    var clamp = value['clampDimension'];

    var clampJson = json.decode(clamp);

    var lampProfileJson;
    if (value['lampProfiles'] != null) {
      var lampProfile = value['lampProfiles'];
      lampProfileJson = json.decode(lampProfile);
    }

    var dummyPole = value['armCount'];
    if (locationCheck == null) {
      String landMark =
          await getLocation(value['latitude'], value['longitude']);
      String? concatenatedLocation = '$manualEnteredLocation,$landMark';
      filteredLandMark = concatenatedLocation
          .replaceAll(RegExp(r',\s*'), ',')
          .replaceAll(RegExp(r'^,\s*'), '')
          .trim();

      var data = {
        "name": value['name'],
        "type": value['type'],
        "customerId": value['customerId'],
        "wardId": value['wardId'],
        "latitude": value['latitude'],
        "longitude": value['longitude'],
        "accuracy": value['accuracy'],
        "location": filteredLandMark,
        "state": value['state'],
        "wardName": value['wardName'],
        "zoneName": value['zoneName'],
        "region": value['region'],
        "installedOn": value['installedOn'],
        "installedBy": value['installedBy'],
        "clampDimension": clampJson,
        "lampProfiles": lampProfileJson,
        "connection": value['connection'],
        "armCount": value['armCount'],
        "auditImg": value['uuidFileName'],
      };
      if (clamp != null) {
        data["clampDimension"] = clampJson;
      }
      if (dummyPole != 0) {
        data["lampProfiles"] = lampProfileJson;
      }

      var result = await detailService.validatePollInstallSucced(
          context, data, dataBloc.token, value['name']);
    } else {
      var data = {
        "name": value['name'],
        "type": value['type'],
        "customerId": value['customerId'],
        "wardId": value['wardId'],
        "latitude": value['latitude'],
        "longitude": value['longitude'],
        "accuracy": value['accuracy'],
        "location": filteredLandMark,
        "state": value['state'],
        "wardName": value['wardName'],
        "zoneName": value['zoneName'],
        "region": value['region'],
        "installedOn": value['installedOn'],
        "installedBy": value['installedBy'],
        "clampDimension": clampJson,
        "lampProfiles": lampProfileJson,
        "armCount": value['armCount'],
        "auditImg": value['uuidFileName'],
      };
      debugPrint(data.toString());
      if (clamp != null) {
        data["clampDimension"] = clampJson;
      }
      if (dummyPole != 0) {
        data["lampProfiles"] = lampProfileJson;
      }
      var result = await detailService.validatePollInstallSucced(
          context, data, dataBloc.token, value['name']);
    }
  }

  Future<String> getLocation(lat, long) async {
    List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
    Placemark place = placemarks[0];
    var locationData =
        '${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
    // location = '${place.name},${place.street},${place.subLocality}, ${place.locality},${place.postalCode},';
    return locationData;
  }

  checkPermission() async {
    // if (value == 'true') {
    //   await AppSettings.openLocationSettings();
    // }
    bool serviceEnabled;
    LocationPermission serviceEnb;
    serviceEnb = await Geolocator.checkPermission();
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    switch (serviceEnb) {
      case LocationPermission.denied:
        serviceEnb = await Geolocator.requestPermission();
        break;
      case LocationPermission.deniedForever:
        await AppSettings.openAppSettings();
        break;
      case LocationPermission.whileInUse:
        null;
        break;
      case LocationPermission.always:
        null;
        break;
      case LocationPermission.unableToDetermine:
        null;
        break;
    }

    if (serviceEnb == LocationPermission.whileInUse ||
        serviceEnb == LocationPermission.always) {
      if (serviceEnabled) {
        setAccuracy(); //modified
      }
    }
  }

  setAccuracy() async {
    bool serviceEnabled;
    Position locationData;
    serviceEnabled =
        await GeolocatorPlatform.instance.isLocationServiceEnabled();
    LocationPermission serviceEnb = await Geolocator.checkPermission();
    if (serviceEnb == LocationPermission.denied ||
        serviceEnb == LocationPermission.deniedForever) {
      checkPermission();
    }
    double minAcc = 1000;
    var lat;
    var long;
    var i1;
    for (var i = 1; i <= 20; i++) {
      locationData = await Geolocator.getCurrentPosition(
        locationSettings: AndroidSettings(accuracy: LocationAccuracy.high),
      );
      var latit = locationData.latitude.toString();
      var longit = locationData.longitude.toString();
      var accura = locationData.accuracy.toString();
      Utility.isConnected().then((value) async {
        if (value) {
          String landMark = await getUserLocation(
              locationData.latitude, locationData.longitude);
        } else {
          null;
        }
      });
      var a = accura == "" ? minAcc : accura;
      String number1 = double.tryParse(a.toString())!.toStringAsFixed(2);
      double accuracy = double.tryParse(number1)!;
      log("I, $i, $accuracy");
      if (mounted) {
        setState(() {
          i1 = i;
        });
      }
      if (accuracy < minAcc) {
        minAcc = accuracy;
        long = longit;
        lat = latit;
        if (minAcc <= 3) {
          break;
        }
      }
      await Future.delayed(const Duration(milliseconds: 500));
    }
    if (i1 == 20) {
      log("OUTPUT , ${long.toString()}, ${lat.toString()}, ${minAcc.toString()}");
      setState(() {
        isLoading = false;
        accuracy = minAcc.toString();
        latitude = lat.toString();
        longitude = long.toString();
        locationLoading = true;
      });
      // debugPrint('$accuracy,$latitude,$longitude,$isLoading');
    }

    return [long.toString(), lat.toString(), minAcc.toString()];
  }

  Future<String> getUserLocation(lat, long) async {
    List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
    Placemark place = placemarks[0];
    if (mounted) {
      setState(() {
        subLocality = place.subLocality;
        location =
            '${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
        // location = '${place.name},${place.street},${place.subLocality}, ${place.locality},${place.postalCode},';
      });
    }
    // debugPrint(location);
    return '${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
  }

  bool isNumeric(String newValue) {
    if (newValue.isEmpty) {
      return false;
    }
    return int.tryParse(newValue) != null;
  }

  takePhoto() async {
    var sucessData = await updatePole(context, widget.token);
    var camerastatus = await Permission.camera.status;
    if (camerastatus.isDenied) {
      await Permission.camera.request();
    }
    final cameras = await availableCameras();
    final firstCamera = cameras.first;
    EasyLoading.dismiss(animation: true);
    if (!mounted) return;
    await Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => TakePictureScreen(
                  token: widget.token,
                  camera: firstCamera,
                  poleNumber: widget.poleNumber,
                )));
  }

  List<DropdownMenuItem<String>> getDropdownItems(String selectedLampType) {
    List<String> lampWattageList;
    switch (selectedLampType) {
      case 'Tube light':
        lampWattageList = tubeLight;
        break;
      case 'SVL':
        lampWattageList = svlLamp;
        break;
      case 'CFL':
        lampWattageList = cflLamp;
        break;
      case 'LED':
        lampWattageList = ledlLamp;
        break;
      case 'T5':
        lampWattageList = t5;
        break;
      case 'BULB':
        lampWattageList = bulb;
        break;
      default:
        lampWattageList = ['40 W'];
    }

    return lampWattageList.map((item) {
      return DropdownMenuItem(
        alignment: AlignmentDirectional.centerStart,
        value: item,
        child: Text(item,
            style: const TextStyle(
              color: Colors.black,
            )),
      );
    }).toList();
  }

  String? getInitialLampWattageForType(String selectedLampType) {
    switch (selectedLampType) {
      case 'Tube light':
        return tubeLight[0];
      case 'SVL':
        return svlLamp[0];
      case 'CFL':
        return cflLamp[0];
      case 'LED':
        return ledlLamp[0];
      case 'T5':
        return t5[0];
      case 'BULB':
        return bulb[0];
      default:
        return '40 W';
    }
  }

  // Update selectedlamps based on dropdownarms value
  void updateSelectedLamps() {
    int newDropdownArms = int.parse(dropdownarms);
    if (selectedlamps.length > newDropdownArms && (newDropdownArms != 0)) {
      //to remove the last appended items
      selectedlamps.removeRange(newDropdownArms, selectedlamps.length);
      // selectedlamps.sublist(selectedlamps.length - newDropdownArms);
    }
  }

  @override
  Widget build(BuildContext context) {
    var size = MediaQuery.of(context).size;
    var height = size.height;
    var width = size.width;
    var dataBloc1 = Provider.of<PoleCountModel>(context, listen: false);
    // double lampTypeWithWattsFieldHeight = 80;
    // int noOfRow = 1;

    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        leading: Builder(builder: (BuildContext context) {
          return IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: bDarkBlue,
            ),
            onPressed: () {
              emptySelectedLampList();
              Navigator.of(context).pop(true);
            },
          );
        }),
        backgroundColor: lightBlue,
        elevation: 0.0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: bDarkBlue,
          fontSize: 18.0,
        ),
        title: Text(
          'PoleVault',
          style: TextStyle(color: bDarkBlue),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                height: 10,
              ),
              Flexible(
                child: SizedBox(
                  height: 50,
                  child: ListView(
                    padding: EdgeInsets.zero,
                    scrollDirection: Axis.horizontal,
                    shrinkWrap: true,
                    physics: const ClampingScrollPhysics(),
                    children: [
                      const SizedBox(
                        width: 10,
                      ),
                      Row(
                        mainAxisAlignment: hasInternet == true
                            ? MainAxisAlignment.spaceEvenly
                            : MainAxisAlignment.center,
                        children: [
                          BoxContainer.rectangleContainer(
                              '$region > $zone > $ward'),
                          const SizedBox(
                            width: 10,
                          ),
                          hasInternet == true
                              ? BoxContainer.numberContainer(
                                  poleCount, Icons.business)
                              : Container(),
                          const SizedBox(
                            width: 10,
                          ),
                          hasInternet == true
                              ? BoxContainer.numberContainer(
                                  specificUserPoleCount,
                                  Icons.person_2_outlined)
                              : Container(),
                        ],
                      ),
                      hasInternet == true
                          ? const SizedBox(
                              width: 10,
                            )
                          : Container(),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              SizedBox(
                width: width * 0.8,
                child: InputDecorator(
                  decoration: InputDecoration(
                    label: RichText(
                        text: TextSpan(
                      text: 'Pole Number',
                      style: TextStyle(
                          color: darkBlue,
                          fontSize: 20,
                          fontWeight: FontWeight.bold),
                    )),
                    labelStyle: TextStyle(
                        color: Colors.black.withOpacity(0.7), fontSize: 20),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  child: Container(
                    padding: const EdgeInsets.only(left: 15),
                    child: Text(
                      pollNumber,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              SizedBox(
                width: width * 0.8,
                // height: 50,
                child: InputDecorator(
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                    label: RichText(
                        text: TextSpan(
                      text: 'Pole Type',
                      style: TextStyle(
                          color: darkBlue,
                          fontSize: 20,
                          fontWeight: FontWeight.bold),
                    )),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton2(
                      value: dropdowntypes,
                      items: types.map((String items) {
                        return DropdownMenuItem(
                          alignment: AlignmentDirectional.centerStart,
                          value: items,
                          child: Text(
                            items,
                            style: const TextStyle(color: Colors.black),
                            // textAlign: TextAlign.left,
                          ),
                        );
                      }).toList(),
                      onChanged: (newValue) {
                        setState(() {
                          dropdowntypes = newValue!;
                          if (dropdowntypes == 'RCC-Normal' ||
                              dropdowntypes == 'RCC-Big' ||
                              dropdowntypes == 'Rail') {
                            dropdownClampType = '6 x 3 inch';
                          } else if (dropdowntypes == 'Octagonal' ||
                              dropdowntypes == 'Tubular') {
                            dropdownClampType = 'Clamp Type Not Required';
                          }
                        });
                      },
                      dropdownStyleData: DropdownStyleData(
                          padding: const EdgeInsets.only(left: 15),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(14),
                            color: Colors.white,
                          )),
                      buttonStyleData: const ButtonStyleData(
                        padding: EdgeInsets.only(),
                        height: 25,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),

              Form(
                key: _formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: width * 0.8,
                      // height: 50,
                      child: InputDecorator(
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          label: RichText(
                              text: TextSpan(
                            text: 'Clamp Type',
                            style: TextStyle(
                                color: darkBlue,
                                fontSize: 20,
                                fontWeight: FontWeight.bold),
                          )),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton2(
                            value: dropdownClampType,
                            items: clampTypes.map((String items) {
                              return DropdownMenuItem(
                                alignment: AlignmentDirectional.centerStart,
                                value: items,
                                child: Text(
                                  items,
                                  style: const TextStyle(color: Colors.black),
                                  // textAlign: TextAlign.left,
                                ),
                              );
                            }).toList(),
                            onChanged: (newValue) {
                              setState(() {
                                dropdownClampType = newValue!;
                              });
                            },
                            dropdownStyleData: DropdownStyleData(
                                padding: const EdgeInsets.only(left: 15),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(14),
                                  color: Colors.white,
                                )),
                            buttonStyleData: const ButtonStyleData(
                              padding: EdgeInsets.only(),
                              height: 25,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    if (dropdownClampType == 'Others')
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 57,
                            width: 56,
                            child: TextFormField(
                              controller: clampTypeLength,

                              // validator: (newValue) {
                              //   if (newValue == null || newValue.isEmpty) {
                              //     FocusScope.of(context)
                              //         .requestFocus(FocusNode());
                              //     return null;
                              //   }
                              //   return null;
                              // },
                              style: const TextStyle(color: Colors.black),
                              textAlign: TextAlign.center,
                              keyboardType: TextInputType.number,

                              maxLength: 2,
                              cursorColor: Theme.of(context).primaryColor,
                              decoration: InputDecoration(
                                  errorStyle: const TextStyle(fontSize: 4),
                                  border: const OutlineInputBorder(),
                                  floatingLabelBehavior:
                                      FloatingLabelBehavior.always,
                                  counterText: '',
                                  labelText: '<L>',
                                  labelStyle: TextStyle(
                                      color: darkBlue,
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold)),
                              onChanged: (newValue) {
                                setState(() {
                                  if (newValue.length == 1 ||
                                      newValue.length == 2) {
                                    // FocusScope.of(context).nextFocus();
                                    customInputLength = newValue;
                                  }
                                });
                              },
                            ),
                          ),
                          const SizedBox(
                            width: 14,
                          ),
                          const Text('x', style: TextStyle(fontSize: 21)),
                          const SizedBox(
                            width: 14,
                          ),
                          SizedBox(
                            height: 57,
                            width: 56,
                            child: TextFormField(
                              controller: clampTypeWidth,
                              style: const TextStyle(color: Colors.black),
                              // autofocus: autoFocus,
                              textAlign: TextAlign.center,
                              keyboardType: TextInputType.number,
                              // controller: controller,
                              maxLength: 2,
                              cursorColor: Theme.of(context).primaryColor,
                              decoration: InputDecoration(
                                  errorStyle: const TextStyle(fontSize: 4),
                                  border: const OutlineInputBorder(),
                                  floatingLabelBehavior:
                                      FloatingLabelBehavior.always,
                                  counterText: '',
                                  labelText: '<W>',
                                  labelStyle: TextStyle(
                                      color: darkBlue,
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold)),
                              onChanged: (newValue) {
                                setState(() {
                                  if (newValue.length == 1 ||
                                      newValue.length == 2) {
                                    // FocusScope.of(context).nextFocus();
                                    customInputWidth = newValue;
                                  }
                                });
                              },
                            ),
                          ),
                          const SizedBox(
                            width: 21,
                          ),
                          SizedBox(
                            height: 60,
                            width: 130,
                            child: InputDecorator(
                              decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  label: RichText(
                                      text: TextSpan(
                                    text: 'Units',
                                    style: TextStyle(
                                        color: darkBlue,
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold),
                                  ))),
                              child: DropdownButtonHideUnderline(
                                  child: DropdownButton2(
                                value: dropdownUnits,
                                items: units.map((String items) {
                                  return DropdownMenuItem(
                                    alignment: AlignmentDirectional.centerStart,
                                    value: items,
                                    child: Text(
                                      items,
                                      style:
                                          const TextStyle(color: Colors.black),
                                    ),
                                  );
                                }).toList(),
                                onChanged: (newValue) {
                                  setState(() {
                                    dropdownUnits = newValue!;
                                  });
                                },
                                dropdownStyleData: DropdownStyleData(
                                    padding: const EdgeInsets.only(left: 15),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(14),
                                      color: Colors.white,
                                    )),
                                buttonStyleData: const ButtonStyleData(
                                  padding: EdgeInsets.only(),
                                  height: 60,
                                ),
                              )),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),

              // ),
              if (dropdownClampType == 'Others')
                const SizedBox(
                  height: 15,
                ),
              SizedBox(
                width: width * 0.8,
                child: InputDecorator(
                  decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      label: RichText(
                          text: TextSpan(
                        text: 'No Of Arms',
                        style: TextStyle(
                            color: darkBlue,
                            fontSize: 20,
                            fontWeight: FontWeight.bold),
                      ))),
                  child: DropdownButtonHideUnderline(
                      child: DropdownButton2(
                    value: dropdownarms,
                    items: arms.map((String items) {
                      return DropdownMenuItem(
                        alignment: AlignmentDirectional.centerStart,
                        value: items,
                        child: Text(
                          items,
                          style: const TextStyle(color: Colors.black),
                        ),
                      );
                    }).toList(),
                    onChanged: (newValue) {
                      setState(() {
                        dropdownarms = newValue!;
                        updateSelectedLamps();
                      });
                    },
                    dropdownStyleData: DropdownStyleData(
                        padding: const EdgeInsets.only(left: 15),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(14),
                          color: Colors.white,
                        )),
                    buttonStyleData: const ButtonStyleData(
                      padding: EdgeInsets.only(),
                      height: 25,
                    ),
                  )),
                ),
              ),
              if (int.parse(dropdownarms) != 0)
                const SizedBox(
                  height: 15,
                ),
              if (int.parse(dropdownarms) != 0)
                ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    reverse: true,
                    itemCount: selectedlamps.length,
                    itemExtent: 80.0,
                    itemBuilder: (BuildContext context, int index) {
                      log(selectedlamps.toString());
                      return Row(
                        children: [
                          if (int.parse(dropdownarms) != 0)
                            Padding(
                              padding: const EdgeInsets.only(left: 39.0),
                              child: SizedBox(
                                  width: width * 0.34,
                                  // width: width * 0.39,
                                  child: InputDecorator(
                                    decoration: InputDecoration(
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                      ),
                                      label: RichText(
                                          text: TextSpan(
                                        text: 'Lamp Type',
                                        style: TextStyle(
                                            color: darkBlue,
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold),
                                      )),
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton2(
                                        // isExpanded: true,
                                        value: selectedlamps[index]['type'],
                                        items: lamp.map((String item) {
                                          return DropdownMenuItem(
                                            alignment: AlignmentDirectional
                                                .centerStart,
                                            value: item,
                                            child: Text(item,
                                                style: const TextStyle(
                                                  color: Colors.black,
                                                )),
                                          );
                                        }).toList(),
                                        // hint: Text('tubelight'),
                                        onChanged: (newValue) {
                                          selectedlamps[index]['type'] =
                                              newValue!;
                                          selectedlamps[index]['watts'] =
                                              getInitialLampWattageForType(
                                                  selectedlamps[index]['type']
                                                      .toString())!;
                                          setState(() {});
                                        },
                                        dropdownStyleData: DropdownStyleData(
                                            // padding: const EdgeInsets.only(left: 13),
                                            decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(14),
                                          color: Colors.white,
                                        )),
                                        buttonStyleData: ButtonStyleData(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 0),
                                            height: 25,
                                            width: width * 0.38),
                                        menuItemStyleData:
                                            const MenuItemStyleData(
                                                padding:
                                                    EdgeInsets.only(left: 20)),
                                      ),
                                    ),
                                  )),
                            ),
                          if (int.parse(dropdownarms) != 0)
                            const SizedBox(
                              width: 5,
                            ),
                          if (int.parse(dropdownarms) != 0)
                            SizedBox(
                              width: width * 0.38,
                              // width: width * 0.39,
                              child: InputDecorator(
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  label: RichText(
                                      text: TextSpan(
                                    text: 'Lamp Wattage',
                                    style: TextStyle(
                                        color: darkBlue,
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold),
                                  )),
                                ),
                                child: DropdownButtonHideUnderline(
                                    child: DropdownButton2(
                                  value: selectedlamps[index]['watts'],
                                  items: getDropdownItems(
                                      selectedlamps[index]['type'].toString()),
                                  onChanged: (newValue) {
                                    setState(() {
                                      selectedlamps[index]['watts'] =
                                          newValue.toString();
                                    });
                                  },
                                  dropdownStyleData: DropdownStyleData(
                                      padding: const EdgeInsets.only(left: 15),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(14),
                                        color: Colors.white,
                                      )),
                                  buttonStyleData: const ButtonStyleData(
                                    padding: EdgeInsets.only(),
                                    height: 25,
                                  ),
                                )),
                              ),
                            ),
                          if (int.parse(dropdownarms) != 0)
                            const SizedBox(
                              width: 5,
                            ),
                          if (index == selectedlamps.length - 1 &&
                              (int.parse(dropdownarms) != 0))
                            Container(
                              height: 50,
                              width: 50,
                              decoration: selectedlamps.length <
                                      int.parse(dropdownarms)
                                  ? BoxDecoration(
                                      color: const Color.fromARGB(
                                              248, 64, 124, 161)
                                          .withOpacity(0.8),
                                      borderRadius: BorderRadius.circular(100))
                                  : BoxDecoration(
                                      color: const Color.fromARGB(
                                          255, 145, 143, 143),
                                      borderRadius: BorderRadius.circular(100)),
                              child: Center(
                                child: Row(
                                  children: [
                                    IconButton(
                                      icon: const Icon(Icons.add),
                                      onPressed: selectedlamps.length <
                                              int.parse(dropdownarms)
                                          ? () {
                                              setState(() {
                                                selectedlamps.add({
                                                  'type': 'Tube light',
                                                  'watts': '40 W'
                                                });
                                              });
                                              // Future.delayed(
                                              //     const Duration(seconds: 5));
                                            }
                                          : null,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      );
                    }),

              const SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Connection',
                    style: TextStyle(
                        fontSize: 16,
                        color: darkBlue,
                        fontWeight: FontWeight.bold),
                  ),
                  SizedBox(
                    width: width * 0.1,
                  ),
                  ToggleButtons(
                    direction: vertical ? Axis.vertical : Axis.horizontal,
                    onPressed: (int index) {
                      setState(() {
                        // The button that is tapped is set to true, and the others to false.
                        for (int i = 0; i < selectedToggle.length; i++) {
                          selectedToggle[i] = i == index;
                          if (index == 0) {
                            toggleValue = 'OH';
                          } else {
                            toggleValue = 'UG';
                          }
                        }
                      });
                      // debugPrint(toggleValue);
                    },
                    borderRadius: const BorderRadius.all(Radius.circular(8)),
                    selectedBorderColor: darkBlue,
                    textStyle: const TextStyle(fontSize: 11),
                    selectedColor: Colors.white,
                    fillColor: darkBlue,
                    color: darkBlue,
                    constraints: BoxConstraints(
                      minHeight: height * 0.05,
                      minWidth: width * 0.2,
                    ),
                    isSelected: selectedToggle,
                    children: toggle,
                  ),
                ],
              ),

              const SizedBox(
                height: 20,
              ),
              SizedBox(
                width: width * 0.8,
                child: InputDecorator(
                    decoration: InputDecoration(
                      label: RichText(
                          text: TextSpan(
                        text: 'Location',
                        style: TextStyle(
                            color: darkBlue,
                            fontSize: 20,
                            fontWeight: FontWeight.bold),
                      )),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                    ),
                    child: Column(
                      children: [
                        // TextFormField(
                        //   maxLength: 150,
                        //   controller: locationAddress,
                        //   decoration: const InputDecoration(
                        //     filled: true,
                        //     counterText: '',
                        //     contentPadding: EdgeInsets.all(16.0),
                        //     hintText: 'Street / LandMark / Building',
                        //   ),
                        //   style: Theme.of(context)
                        //       .textTheme
                        //       .bodySmall!
                        //       .copyWith(fontSize: 14.0),
                        //   keyboardType: TextInputType.text,
                        // ),
                        AnimatedTextField(
                          animationType: Animationtype.typer,
                          keyboardType: TextInputType
                              .visiblePassword, // to remove emoji from the keyboard
                          maxLength: 100,

                          controller: locationAddress,
                          hintTexts: const [
                            'Provide additional landmark information if any',
                          ],
                          style: const TextStyle(color: Colors.black),
                          hintTextStyle: const TextStyle(
                            fontSize: 13,
                          ),

                          decoration: InputDecoration(
                              counterText: '',
                              prefixIcon:
                                  const Icon(Icons.location_on_outlined),
                              contentPadding: const EdgeInsets.all(16.0),
                              filled: true,
                              fillColor: const Color.fromARGB(248, 64, 124, 161)
                                  .withOpacity(0.20),
                              focusedBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                    color: Color.fromARGB(248, 64, 124, 161),
                                    width: 1.0),
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: const BorderSide(
                                    color: Color.fromARGB(248, 64, 124, 161),
                                    width: 1.0),
                                borderRadius: BorderRadius.circular(8.0),
                              )),
                        ),

                        const SizedBox(height: 20),
                        locationLoading == true
                            ? Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  location != null
                                      ? Text(
                                          location!,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                              fontSize: 13,
                                              color: bDarkBlue,
                                              fontWeight: FontWeight.bold),
                                        )
                                      : Container(),
                                  const SizedBox(
                                    height: 2,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      const Text(
                                        'Latitude : ',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        latitude,
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const Text(
                                        'Longitude : ',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontSize: 12,
                                        ),
                                      ),
                                      Text(
                                        longitude,
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  )
                                ],
                              )
                            : Center(
                                child: Text(
                                'Fetching Location...',
                                style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: bDarkBlue),
                              ))
                      ],
                    )),
              ),
              const SizedBox(
                height: 10,
              ),
              GestureDetector(
                  child: Container(
                    height: 50,
                    width: width * 0.8,
                    decoration: BoxDecoration(
                        color: const Color.fromARGB(248, 64, 124, 161)
                            .withOpacity(0.8),
                        borderRadius: BorderRadius.circular(10)),
                    child: const Center(
                      child: Text(
                        'Save & Proceed to Photo',
                        style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16),
                      ),
                    ),
                  ),
                  onTap: () async {
                    if (isLoading == false) {
                      if (latitude != 'null' && longitude != 'null') {
                        if (dropdownClampType == 'Others') {
                          if (clampTypeLength.text.isEmpty ||
                              clampTypeWidth.text.isEmpty) {
                            FocusScope.of(context).requestFocus(FocusNode());
                            showToastMessage(
                                context, "Please provide the length and width");
                          } else if (!isNumeric(clampTypeLength.text) ||
                              !isNumeric(clampTypeWidth.text)) {
                            FocusScope.of(context).requestFocus(FocusNode());
                            showToastMessage(
                                context, "Length and width must be numeric");
                          } else {
                            // FocusScope.of(context).requestFocus(FocusNode());
                            takePhoto();
                          }
                        } else {
                          takePhoto();
                        }
                      }
                    } else {
                      alertPopUp(context, 'Collecting Data Please Wait',
                          'assets/animation/userAlert.json');
                    }
                  }),
              const SizedBox(
                height: 10,
              ),
              hasInternet == false
                  ? Container(
                      height: 50,
                      width: width * 0.8,
                      decoration: BoxDecoration(
                          color: const Color(0xFF666666),
                          borderRadius: BorderRadius.circular(16)),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.cloud_off_outlined,
                            color: Colors.white,
                          ),
                          Text(
                            '  Offline!',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold),
                          ),
                        ],
                      ))
                  : Container(),
              const SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
      ),
    ));
  }

  void emptySelectedLampList() {
    selectedlamps = [];
  }

  dynamic getUniqueLampTypes(selectedLampType) {
    final List<Map<String, dynamic>> inputList = selectedLampType;

    final List<Map<String, dynamic>> outputList = [];

    for (final item in inputList) {
      final lampType = item['type'] as String;
      final wattageStr = item['watts'] as String;
      final wattage = int.tryParse(wattageStr.replaceAll(RegExp(r'\D'), ''));

      if (wattage != null) {
        final hasDuplicate = outputList
            .any((map) => map['type'] == lampType && map['watts'] == wattage);

        if (!hasDuplicate) {
          final map = {
            'type': lampType,
            'watts': wattage,
          };
          outputList.add(map);
        }
      }
    }
    return outputList;
  }

  updatePole(context, token) async {
    loaderAnimation('loading...');
    int? intCustomLength;
    int? intCustomWidth;
    int? intClampLength;
    int? intClampWidth;
    String? filteredLocation;
    var uuid = const Uuid();
    var fileName = uuid.v1();
    // String? filteredmanualEnteredLocation;

    String stringDropdownlampWatt = dropdownlampWatt.replaceAll(" W", "");
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('manualEnteredLocation', locationAddress.text);
    manualEnteredLocation = locationAddress.text;
    String? concatenatedmanualEnteredLocation;
    if (dropdownClampType != 'Clamp Type Not Required') {
      if (dropdownClampType == 'Others') {
        intCustomLength = int.parse(customInputLength);
        intCustomWidth = int.parse(customInputWidth);
      } else {
        List<String> splittedClampType = dropdownClampType.split(' ');
        intClampLength = int.parse(splittedClampType[0]);
        intClampWidth = int.parse(splittedClampType[2]);
      }
    }
    if (location != null) {
      filteredLocation = location!
          .replaceAll(RegExp(r',\s*'), ',')
          .replaceAll(RegExp(r'^,\s*'), '')
          .trim();
    }
    // concatenatedmanualEnteredLocation =
    //     "$manualEnteredLocation,$filteredLocation";
    // filteredmanualEnteredLocation = concatenatedmanualEnteredLocation
    //     .replaceAll(RegExp(r',\s*'), ',')
    //     .replaceAll(RegExp(r'^,\s*'), '')
    //     .replaceAll(RegExp(r'^,\s*'), 'null')
    //     .trim();
    // print(filteredmanualEnteredLocation);

    var poleCountData = Provider.of<PoleCountModel>(context, listen: false);
    poleCountData.name = widget.poleNumber;
    poleCountData.type = dropdowntypes;
    poleCountData.customerId = widget.customerId;
    poleCountData.latitude = double.parse(latitude);
    poleCountData.longitude = double.parse(longitude);
    poleCountData.accuracy = double.parse(accuracy);
    poleCountData.location = filteredLocation;
    poleCountData.manualEnteredLocation = manualEnteredLocation;
    poleCountData.state = state;
    poleCountData.wardName = widget.ward;
    poleCountData.zoneName = widget.zone;
    poleCountData.region = widget.region;
    poleCountData.installedOn = int.parse(widget.installedOn);
    poleCountData.clampDimension = dropdownClampType == 'Others'
        ? {
            'length': intCustomLength,
            'width': intCustomWidth,
            'unit': dropdownUnits,
          }
        : dropdownClampType == 'Clamp Type Not Required'
            ? null
            : {
                'length': intClampLength,
                'width': intClampWidth,
                'unit': 'inch',
              };

    // poleCountData.clampDimension = dropdownClampType == 'Others'
    //     ? {
    //         'length': intCustomLength,
    //         'width': intCustomWidth,
    //         'unit': dropdownUnits,
    //       }
    //     : {'length': intClampLength, 'width': intClampWidth, 'unit': 'inch'};
    log(poleCountData.clampDimension.toString());
    if (int.parse(dropdownarms) != 0) {
      poleCountData.lampProfiles = getUniqueLampTypes(selectedlamps);
      log(poleCountData.lampProfiles.toString());
    }
    poleCountData.connection = toggleValue;
    poleCountData.armCount = int.parse(dropdownarms);
    poleCountData.installedBy = widget.installedBy;
    poleCountData.wardId = widget.wardId;
    poleCountData.uuidFileName = '$fileName.jpg';
    var insertDetails =
        await DatabaseRepo().insertDetailsData('tbl_pole_db', poleCountData);
    var storePoleCount = Provider.of<PoleCountModel>(context, listen: false);
    storePoleCount.poleCountName = poleCountData.name;
    storePoleCount.poleCountWardId = poleCountData.wardId;
    var insertPoleCount = await DatabaseRepo()
        .insertPoleCountData('tbl_polecount_db', storePoleCount);
    debugPrint(insertPoleCount.toString());
    incree();

    // var result =
    //     await _service.validatePollInstallSucced(context, data1, token);
    // return result;
  }

  incree() async {
    List localresult =
        await DatabaseRepo().readDateDatabyPoleName('tbl_date_db', todaysDate);
    if (localresult.isNotEmpty) {
      int inc = localresult[0]['polecount'];
      int inc1 = inc + 1;
      var localresult1 = await DatabaseRepo()
          .increasePoleCountData('tbl_date_db', inc1, todaysDate);
      // print(localresult1);
    }
  }
}
