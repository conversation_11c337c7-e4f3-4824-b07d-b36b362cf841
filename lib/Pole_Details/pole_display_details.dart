import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:app_settings/app_settings.dart';
import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geocoding/geocoding.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:schnell_pole_installation/DB/db_repo.dart';
import 'package:schnell_pole_installation/Provider/provider.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_details.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_details_controller.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/pole_installation_controller.dart';
import 'package:schnell_pole_installation/take_photo/take_photo_controller.dart';
import 'package:schnell_pole_installation/utils/box_container.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/loader.dart';
import 'package:schnell_pole_installation/utils/utility.dart';
import '../take_photo/image_address_append.dart';

class PoleDisplayDetails extends StatefulWidget {
  const PoleDisplayDetails({
    super.key,
    required this.region,
    required this.zone,
    required this.ward,
    required this.poleNumber,
    required this.poleType,
    required this.noOfArms,
    required this.clampDimension,
    required this.lampProfiles,
    // required this.lampType,
    // required this.lampWattage,
    required this.customerId,
    required this.wardId,
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.location,
    required this.state,
    required this.installedOn,
    required this.installedBy,
    required this.auditPicS3Url,
    required this.connection,
    required this.token,
    required this.poleCount,
    required this.specificUserPoleCount,
    required this.defaultregion,
    required this.defaultzone,
    required this.defaultward,
  });
  final String defaultregion;
  final String defaultzone;
  final String defaultward;
  final String region;
  final String zone;
  final String ward;
  final String poleNumber;
  final String poleType;
  final String noOfArms;
  final String clampDimension;
  final String lampProfiles;
  final int specificUserPoleCount;
  // final String lampType;
  // final String lampWattage;
  final String customerId;
  final String wardId;
  final String latitude;
  final String longitude;
  final String accuracy;
  final String location;
  final String state;
  final String installedOn;
  final String installedBy;
  final String auditPicS3Url;
  final String connection;
  final String token;
  final String poleCount;

  @override
  State<PoleDisplayDetails> createState() => _PoleDisplayDetailsState();
}

class _PoleDisplayDetailsState extends State<PoleDisplayDetails> {
  String? dropdowntypes;
  String? dropdownarms;
  String? dropdownLocation;
  String? dropdownClampType;
  int? clampTypeLength;
  int? clampTypeWidth;
  String? clampTypeUnits;
  List<dynamic>? dropdownLampProfiles;
  // String? dropdownlampType;
  // String? dropdownlampWatt;
  String? poleNumber;
  String? dropdownconnection;
  String? subLocality;
  bool vertical = false;
  String toggleValue = '';
  List<bool> selectedToggle = <bool>[true, false];
  List<Widget> toggle = <Widget>[
    const Text('Overhead'),
    const Text('Underground'),
  ];
  final PoleInstallationController _service = PoleInstallationController();
  final PoleDetailController detailService = PoleDetailController();
  final TakePhotoController imageService = TakePhotoController();
  // late Timer timer;
  bool internetLoader = false;
  late Timer sendDatatimer;
  late Timer sendImagetimer;
  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;
  // List<Map<String, String>> lampTypeLabel = [
  //   {"lampType": "Tube light", "lampWattage": "40 W"},
  //   {"lampType": "Tube light", "lampWattage": "40 W"},
  //   {"lampType": "CFL", "lampWattage": "40 W"}
  // ];

  @override
  void initState() {
    dropdownarms = widget.noOfArms;
    dropdowntypes = widget.poleType;
    dropdownClampType = widget.clampDimension;
    dropdownLocation = widget.location;
    if (int.parse(dropdownarms!) != 0) {
      dropdownLampProfiles = jsonDecode(widget.lampProfiles);
    }
    // dropdownlampType = widget.lampType;
    // dropdownlampWatt = widget.lampWattage;
    poleNumber = widget.poleNumber;
    toggleValue = widget.connection;
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    initConnectivity(context);
    // checkInternet(context);
    // timer = Timer.periodic(const Duration(minutes: 5), (timer) {
    //   checkInternet(context);
    // });
    sendDatatimer = Timer.periodic(const Duration(minutes: 10), (value) {
      autoUploadData(context);
    });
    sendImagetimer = Timer.periodic(const Duration(minutes: 10), (value) {
      autoUploadImage(context);
    });
    super.initState();
  }

  @override
  void dispose() {
    // timer.cancel();
    _connectivitySubscription.cancel();
    sendImagetimer.cancel();
    sendDatatimer.cancel();
    super.dispose();
  }

  Future<void> initConnectivity(BuildContext context) async {
    late List<ConnectivityResult> result;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
      debugPrint('Connection Status : $result');
    } on PlatformException catch (e) {
      debugPrint(
        'Couldn\'t check connectivity status : $e',
      );
      return;
    }
    if (!mounted) {
      return Future.value(null);
    }

    return _updateConnectionStatus(result);
  }

  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    setState(() {
      _connectionStatus = result;
    });
    debugPrint('Update Connection Status : $_connectionStatus');
    if (_connectionStatus.contains(ConnectivityResult.none) ||
        _connectionStatus.isEmpty) {
      setState(() {
        internetLoader = false;
      });
    } else {
      setState(() {
        internetLoader = true;
      });
    }
  }

  checkInternet(BuildContext context) {
    Utility.isConnected().then((value) {
      if (value) {
        setState(() {
          internetLoader = true;
        });
      } else {
        // internetsnackBar(context, snacBarTitle, snackBarMessage);
        setState(() {
          internetLoader = false;
        });
      }
    });
  }

  autoUploadData(BuildContext context) async {
    Utility.isConnected().then((value) async {
      if (value) {
        List datalength =
            await DatabaseRepo().readAllDataDetails('tbl_pole_db');
        if (datalength.isNotEmpty) {
          for (int i = 1; i <= datalength.length; i++) {
            var localresult =
                await DatabaseRepo().readOneDetailsData('tbl_pole_db');
            await dataUpdation(localresult[0], context);
          }
        }
      } else {
        null;
      }
    });
  }

  autoUploadImage(BuildContext context) async {
    Utility.isConnected().then((value) async {
      if (value) {
        List datalength =
            await DatabaseRepo().readAllDataDetails('tbl_image_db');
        if (datalength.isNotEmpty) {
          for (int i = 1; i <= datalength.length; i++) {
            var localresult =
                await DatabaseRepo().readOneDetailsData('tbl_image_db');
            await imageUpdation(localresult[0], context);
          }
        }
      } else {
        null;
      }
    });
  }

  imageUpdation(imageData, BuildContext context) async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    ImageAddressAppender appender = ImageAddressAppender();
    String imageBase64 = imageData['deviceImage'];
    var locationCheck = imageData['location'];
    String wtrmrkLocation = locationCheck == null || locationCheck == ''
        ? await getUserLocation(imageData['latitude'], imageData['longitude'])
        : locationCheck;
    Uint8List imageBytes = base64Decode(imageBase64);
    // Get the directory to save the image
    Directory tempDir = await getTemporaryDirectory();
    String tempPath = tempDir.path;
    File imageFile =
        File('$tempPath/image.png'); //Create a file to write the image bytes
    await imageFile
        .writeAsBytes(imageBytes); //Write the image bytes to the file

    // Step 5: Return the file path
    // return imageFile.path;
    await appender.appendAddressToImage(
      imageFile.path,
      imageData['wtrmrkLatitude'],
      imageData['wtrmrkLongitude'],
      imageData['imagePoleName'] ?? '',
      imageData['wtrmrkAccuracy'],
      imageData['wtrmrkManualEnteredLocation'] ?? '',
      wtrmrkLocation,
      imageData['wtrmrkRegion'] ?? '',
      imageData['wtrmrkZone'] ?? '',
      imageData['wtrmrkWard'] ?? '',
    );
    final bytes = File(imageFile.path).readAsBytesSync();
    String deviceImage = base64Encode(bytes);
    var result = await imageService.imageUpdation(
        deviceImage, dataBloc.token, imageData, context);
  }

  dataUpdation(value, BuildContext context) async {
    var dataBloc = Provider.of<DataModel>(context, listen: false);
    var locationCheck = value['location'];
    var manualEnteredLocation = value['manualEnteredLocation'];
    String? filteredLandMark;
    if (locationCheck != null) {
      String? concatenatedLocation = '$manualEnteredLocation,$locationCheck';
      filteredLandMark = concatenatedLocation
          .replaceAll(RegExp(r',\s*'), ',')
          .replaceAll(RegExp(r'^,\s*'), '')
          .trim();
    }

    var clamp = value['clampDimension'];
    var clampJson = json.decode(clamp);

    var lampProfileJson;
    if (value['lampProfiles'] != null) {
      var lampProfile = value['lampProfiles'];
      lampProfileJson = json.decode(lampProfile);
    }

    var dummyPole = value['armCount'];

    if (locationCheck == null) {
      String landMark =
          await getUserLocation(value['latitude'], value['longitude']);
      String? concatenatedLocation = '$manualEnteredLocation,$landMark';
      filteredLandMark = concatenatedLocation
          .replaceAll(RegExp(r',\s*'), ',')
          .replaceAll(RegExp(r'^,\s*'), '')
          .trim();

      var data = {
        "name": value['name'],
        "type": value['type'],
        "customerId": value['customerId'],
        "wardId": value['wardId'],
        "latitude": value['latitude'],
        "longitude": value['longitude'],
        "accuracy": value['accuracy'],
        "location": filteredLandMark,
        "state": value['state'],
        "wardName": value['wardName'],
        "zoneName": value['zoneName'],
        "region": value['region'],
        "installedOn": value['installedOn'],
        "installedBy": value['installedBy'],
        "connection": value['connection'],
        "armCount": value['armCount'],
        "auditImg": value['uuidFileName'],
      };
      if (clamp != null) {
        data["clampDimension"] = clampJson;
      }
      if (dummyPole != 0) {
        data["lampProfiles"] = lampProfileJson;
      }
      var result = await detailService.validatePollInstallSucced(
          context, data, dataBloc.token, value['name']);
    } else {
      var data = {
        "name": value['name'],
        "type": value['type'],
        "customerId": value['customerId'],
        "wardId": value['wardId'],
        "latitude": value['latitude'],
        "longitude": value['longitude'],
        "accuracy": value['accuracy'],
        "location": filteredLandMark,
        "state": value['state'],
        "wardName": value['wardName'],
        "zoneName": value['zoneName'],
        "region": value['region'],
        "installedOn": value['installedOn'],
        "installedBy": value['installedBy'],
        "connection": value['connection'],
        "armCount": value['armCount'],
        "auditImg": value['uuidFileName'],
      };
      if (clamp != null) {
        data["clampDimension"] = clampJson;
      }
      if (dummyPole != 0) {
        data["lampProfiles"] = lampProfileJson;
      }
      var result = await detailService.validatePollInstallSucced(
          context, data, dataBloc.token, value['name']);
    }
  }

  Future<String> getUserLocation(lat, long) async {
    List<Placemark> placemarks = await placemarkFromCoordinates(lat, long);
    Placemark place = placemarks[0];
    var locationData =
        '${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.country}, ${place.postalCode}';
    // location = '${place.name},${place.street},${place.subLocality}, ${place.locality},${place.postalCode},';
    return locationData;
  }

  @override
  Widget build(BuildContext context) {
    if (dropdownClampType != "") {
      Map<String, dynamic> clampTypeList = json.decode(dropdownClampType!);
      clampTypeLength = clampTypeList['length'];
      clampTypeWidth = clampTypeList['width'];
      clampTypeUnits = clampTypeList['unit'];
    }

    if (toggleValue == 'OH') {
      selectedToggle = [true, false];
    } else if (toggleValue == 'UG') {
      selectedToggle = [false, true];
    }

    String modifiedLocation = dropdownLocation!
        .replaceAll(RegExp(r',\s*'), ',')
        .replaceAll(RegExp(r'^,\s*'), '')
        .trim();

    print(modifiedLocation);

    var size = MediaQuery.of(context).size;
    var height = size.height;
    var width = size.width;
    var widthFull = width * 0.8;
    var widthHalf = width * 0.39;
    Uint8List bytes = widget.auditPicS3Url.contains('luminator-iotpro') ||
            widget.auditPicS3Url.contains('schnell')
        ? base64Decode('')
        : base64Decode(widget.auditPicS3Url);
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        leading: Builder(builder: (BuildContext context) {
          return IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: bDarkBlue,
            ),
            onPressed: () {
              Navigator.of(context).pop(true);
            },
          );
        }),
        backgroundColor: lightBlue,
        elevation: 0.0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: bDarkBlue,
          fontSize: 18.0,
        ),
        title: Text(
          'PoleVault',
          style: TextStyle(color: bDarkBlue),
        ),
        // actions: [
        //   IconButton(
        //       onPressed: () async {
        //         bool shouldExit = await showExitPopup(context);
        //         if (shouldExit) {
        //           SystemNavigator.pop();
        //         }
        //       },
        //       icon: Icon(
        //         Icons.logout,
        //         color: bDarkBlue,
        //       ))
        // ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(
                height: 10,
              ),
              SizedBox(
                height: 50,
                child: ListView(
                  padding: EdgeInsets.zero,
                  scrollDirection: Axis.horizontal,
                  shrinkWrap: true,
                  physics: const ClampingScrollPhysics(),
                  children: [
                    const SizedBox(
                      width: 10,
                    ),
                    Row(
                      mainAxisAlignment: widget.poleCount != ''
                          ? MainAxisAlignment.spaceEvenly
                          : MainAxisAlignment.center,
                      children: [
                        BoxContainer.rectangleContainer(
                            '${widget.defaultregion} > ${widget.defaultzone} > ${widget.defaultward}'),
                        const SizedBox(
                          width: 10,
                        ),
                        widget.poleCount != ''
                            ? BoxContainer.numberContainer(
                                '${widget.poleCount}', Icons.business)
                            : Container(),
                        const SizedBox(
                          width: 10,
                        ),
                        widget.specificUserPoleCount != 0
                            ? BoxContainer.numberContainer(
                                '${widget.specificUserPoleCount}',
                                Icons.person_2_outlined)
                            : Container(),
                      ],
                    ),
                    widget.poleCount != ''
                        ? const SizedBox(
                            width: 10,
                          )
                        : Container(),
                  ],
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              Center(
                child: Text(
                  'Pole Number : ${poleNumber!}',
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              BoxContainer.dropdownValueContainer(
                  widthFull, dropdowntypes, 'Pole Type'),
              const SizedBox(
                height: 15,
              ),
              BoxContainer.dropdownValueContainer(
                  widthFull, dropdownarms, 'No Of Arms'),
              const SizedBox(
                height: 15,
              ),
              if (dropdownClampType != "")
                BoxContainer.dropdownValueContainer(
                    widthFull,
                    '$clampTypeLength x $clampTypeWidth $clampTypeUnits',
                    'Clamp Type'),
              if (dropdownClampType != "")
                const SizedBox(
                  height: 15,
                ),
              if (int.parse(dropdownarms!) != 0)
                SizedBox(
                  width: widthFull,
                  child: InputDecorator(
                    decoration: InputDecoration(
                      label: RichText(
                          text: TextSpan(
                        text: "Lamp Profile",
                        style: TextStyle(
                            color: darkBlue,
                            fontSize: 20,
                            fontWeight: FontWeight.bold),
                      )),
                      labelStyle: TextStyle(
                          color: Colors.black.withOpacity(0.7), fontSize: 20),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                    ),
                    child: Wrap(
                      runSpacing: 6,
                      spacing: 3,
                      alignment: WrapAlignment.start,
                      direction: Axis.horizontal,
                      children:
                          List.generate(dropdownLampProfiles!.length, (index) {
                        String lampType = '';
                        String lampWatt = '';
                        if (index < dropdownLampProfiles!.length) {
                          lampType =
                              dropdownLampProfiles![index]['type'].toString();
                          lampWatt =
                              dropdownLampProfiles![index]['watts'].toString();
                          print(
                              "Lamp Type: $lampType, Lamp Wattage: $lampWatt");
                        }
                        return SizedBox(
                          height: MediaQuery.of(context).size.height / 25,
                          width: MediaQuery.of(context).size.width / 2 - 100,
                          child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6),
                                color: const Color.fromARGB(248, 215, 219, 221),
                              ),
                              // child: FittedBox(
                              //     fit: BoxFit.contain,
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Center(
                                    child: Text(
                                  "$lampType, $lampWatt",
                                  // overflow: TextOverflow.clip,
                                  style: const TextStyle(fontSize: 13),
                                )),
                              )
                              // )
                              ),
                        );
                      }),
                    ),
                  ),
                ),
              if (int.parse(dropdownarms!) != 0)
                const SizedBox(
                  height: 15,
                ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Connection',
                    style: TextStyle(
                        fontSize: 15,
                        color: darkBlue,
                        fontWeight: FontWeight.bold),
                  ),
                  SizedBox(
                    width: width * 0.1,
                  ),
                  IgnorePointer(
                    ignoring: true,
                    child: ToggleButtons(
                      direction: vertical ? Axis.vertical : Axis.horizontal,
                      onPressed: (int index) {
                        setState(() {
                          // The button that is tapped is set to true, and the others to false.
                          for (int i = 0; i < selectedToggle.length; i++) {
                            selectedToggle[i] = i == index;
                            if (index == 0) {
                              toggleValue = 'OH';
                            } else {
                              toggleValue = 'UG';
                            }
                          }
                        });
                        debugPrint(toggleValue);
                      },
                      borderRadius: const BorderRadius.all(Radius.circular(8)),
                      selectedBorderColor: darkBlue,
                      textStyle: const TextStyle(fontSize: 11),
                      selectedColor: Colors.white,
                      fillColor: darkBlue,
                      color: darkBlue,
                      constraints: BoxConstraints(
                        minHeight: height * 0.05,
                        minWidth: width * 0.2,
                      ),
                      isSelected: selectedToggle,
                      children: toggle,
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
              SizedBox(
                width: width * 0.8,
                child: InputDecorator(
                  decoration: InputDecoration(
                    label: RichText(
                        text: TextSpan(
                      text: 'Location',
                      style: TextStyle(
                          color: darkBlue,
                          fontSize: 21,
                          fontWeight: FontWeight.bold),
                    )),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      widget.defaultregion != widget.region ||
                              widget.defaultward != widget.ward ||
                              widget.defaultzone != widget.zone
                          ? FittedBox(
                              child: Container(
                                height: 50,
                                decoration: BoxDecoration(
                                    color:
                                        const Color.fromARGB(248, 64, 124, 161)
                                            .withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(18)),
                                child: Center(
                                  child: Row(
                                    children: [
                                      const SizedBox(
                                        width: 7.5,
                                      ),
                                      const Icon(Icons.location_on_outlined,
                                          color: Colors.white),
                                      const SizedBox(
                                        width: 7.5,
                                      ),
                                      Text(
                                        '${widget.region} > ${widget.zone} > ${widget.ward}',
                                        style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold),
                                      ),
                                      const SizedBox(
                                        width: 15,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          : Container(),
                      widget.defaultregion != widget.region ||
                              widget.defaultward != widget.ward ||
                              widget.defaultzone != widget.zone
                          ? const SizedBox(
                              height: 2,
                            )
                          : Container(),
                      widget.location != ''
                          ? Text(
                              modifiedLocation,
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  fontSize: 13,
                                  color: bDarkBlue,
                                  fontWeight: FontWeight.bold),
                            )
                          : Container(),
                      widget.location != ''
                          ? const SizedBox(
                              height: 2,
                            )
                          : Container(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          const Text(
                            'Latitude : ',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            widget.latitude,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                                fontSize: 12, fontWeight: FontWeight.bold),
                          ),
                          const Text(
                            'Longitude : ',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            widget.longitude,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                                fontSize: 12, fontWeight: FontWeight.bold),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              widget.auditPicS3Url != ''
                  ? SizedBox(
                      width: width * 0.8,
                      // child: FadeInImage(placeholder: , image: image),
                      child: widget.auditPicS3Url
                                  .contains('luminator-iotpro') ||
                              widget.auditPicS3Url.contains('schnell')
                          ? Image.network(
                              widget.auditPicS3Url,
                              loadingBuilder:
                                  (context, child, loadingProgress) {
                                if (loadingProgress == null) return child;
                                return Center(
                                  child: CircularProgressIndicator(
                                    color: darkBlue,
                                    value: loadingProgress.expectedTotalBytes !=
                                            null
                                        ? loadingProgress.cumulativeBytesLoaded
                                                .toDouble() /
                                            loadingProgress.expectedTotalBytes!
                                                .toDouble()
                                        : null,
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return Center(
                                  child: Text(
                                    'No Image Available!',
                                    style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.bold,
                                        color: bDarkBlue),
                                  ),
                                );
                              },
                            )
                          : Image.memory(
                              Uint8List.fromList(bytes),
                              errorBuilder: (context, error, stackTrace) {
                                return Center(
                                  child: Text(
                                    'No Image Available!',
                                    style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.bold,
                                        color: bDarkBlue),
                                  ),
                                );
                              },
                            ),
                    )
                  : Center(
                      child: Text(
                        'No Image Available!',
                        style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                            color: bDarkBlue),
                      ),
                    ),
              const SizedBox(
                height: 20,
              ),
              Container(
                height: 50,
                width: width * 0.8,
                decoration: BoxDecoration(
                    color: const Color.fromARGB(248, 217, 232, 243),
                    borderRadius: BorderRadius.circular(10)),
                child: Center(
                    child: Text(
                  widget.state,
                  style: const TextStyle(fontSize: 16),
                  textAlign: TextAlign.center,
                )),
              ),
              const SizedBox(
                height: 20,
              ),
              internetLoader == false
                  ? Container(
                      height: 50,
                      width: width * 0.8,
                      decoration: BoxDecoration(
                          color: const Color(0xFF666666),
                          borderRadius: BorderRadius.circular(16)),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.cloud_off_outlined,
                            color: Colors.white,
                          ),
                          Text(
                            '  Offline!',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold),
                          ),
                        ],
                      ))
                  : Container(),
              const SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          scanBarCode();
        },
        backgroundColor: bDarkBlue,
        child: const Icon(Icons.qr_code),
      ),
    ));
  }

  // scanBarCode() async {
  //   final result = await BarcodeScanner.scan(
  //       options: const ScanOptions(strings: {
  //     'cancel': 'CANCEL',
  //     'flash_on': 'Flash on',
  //     'flash_off': 'Flash off'
  //   }));
  //   if (result.rawContent != '') {
  //     loaderAnimation('loading...');
  //     debugPrint(result.rawContent);
  //     final validCharacters = RegExp(r'^[a-zA-Z0-9]+$');
  //     final verify = validCharacters.hasMatch(result.rawContent);
  //     if (result.rawContent.length == 7 &&
  //         validCharacters.hasMatch(result.rawContent)) {
  //       EasyLoading.dismiss(animation: true);
  //       Utility.isConnected().then((value) {
  //         if (value) {
  //           var data = _service.validatePoll(
  //               result.rawContent,
  //               context,
  //               widget.token,
  //               '1',
  //               widget.poleCount,
  //               widget.defaultregion,
  //               widget.defaultzone,
  //               widget.defaultward);
  //         } else {
  //           internetsnackBar(context, snacBarTitle, snackBarMessage);
  //         }
  //       });
  //     } else if (!validCharacters.hasMatch(result.rawContent)) {
  //       EasyLoading.dismiss(animation: true);
  //       alertPopUp(
  //           context,
  //           'Pole number format is Invalid. Kindly scan a different QR code.',
  //           'assets/animation/warn.json');
  //     } else if (result.rawContent.length != 7) {
  //       EasyLoading.dismiss(animation: true);
  //       alertPopUp(
  //           context,
  //           'Pole number format is Invalid. Kindly scan a different QR code.',
  //           'assets/animation/warn.json');
  //     } else {
  //       EasyLoading.dismiss(animation: true);
  //     }
  //   }
  // }
  scanBarCode() async {
    final dataBloc = Provider.of<DataModel>(context, listen: false);
    PermissionStatus camera = await Permission.camera.status;
    if (camera == PermissionStatus.granted) {
      final result = await BarcodeScanner.scan(
          options: const ScanOptions(strings: {
        'cancel': 'CANCEL',
        'flash_on': 'Flash on',
        'flash_off': 'Flash off'
      }));
      if (result.rawContent != '') {
        loaderAnimation('loading...');
        // debugPrint(result.rawContent);
        String trimmedContent = result.rawContent.trim();

        final validCharacters = RegExp(r'^[a-zA-Z0-9]+$');
        if (trimmedContent.length == 7 ||
            trimmedContent.length == 8 &&
                validCharacters.hasMatch(trimmedContent)) {
          EasyLoading.dismiss(animation: true);
          Utility.isConnected().then((value) async {
            if (value) {
              // var localresult = await DatabaseRepo().readDatabyPoleName('tbl_pole_db',result.rawContent);
              // print(localresult);
              var data = _service.validatePoll(
                  trimmedContent,
                  context,
                  dataBloc.token,
                  '1',
                  widget.poleCount,
                  widget.specificUserPoleCount,
                  dataBloc.region,
                  dataBloc.zone,
                  dataBloc.ward);
            } else {
              List data = await DatabaseRepo()
                  .readDetailsDatabyPoleName('tbl_pole_db', trimmedContent);
              if (data.isEmpty) {
                var dateTime = DateTime.now().millisecondsSinceEpoch.toString();
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => PoleDetails(
                            token: dataBloc.token,
                            region: dataBloc.region,
                            zone: dataBloc.zone,
                            ward: dataBloc.ward,
                            poleNumber: trimmedContent,
                            customerId: dataBloc.customerId,
                            wardId: dataBloc.wardId,
                            installedOn: dateTime,
                            installedBy: dataBloc.userName,
                            poleCount: widget.poleCount,
                            specificUserPoleCount:
                                widget.specificUserPoleCount)));
              } else {
                List dataImage = await DatabaseRepo()
                    .readDetailsDatabyPoleName('tbl_image_db', trimmedContent);

                Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                        builder: (context) => PoleDisplayDetails(
                              token: dataBloc.token,
                              region: data[0]['region'],
                              zone: data[0]['zoneName'],
                              ward: data[0]['wardName'],
                              poleNumber: data[0]['name'],
                              clampDimension: data[0]['clampDimension'] == null
                                  ? ''
                                  : data[0]['clampDimension'],
                              poleType: data[0]['type'],
                              noOfArms: data[0]['armCount'].toString(),
                              lampProfiles: data[0]['lampProfiles'] ?? '',
                              customerId: data[0]['customerId'],
                              wardId: data[0]['wardId'],
                              latitude: data[0]['latitude'].toString(),
                              longitude: data[0]['longitude'].toString(),
                              accuracy: data[0]['accuracy'].toString(),
                              location: data[0]['location'] == null
                                  ? ''
                                  : data[0]['location'],
                              state: data[0]['state'],
                              installedOn: data[0]['installedOn'].toString(),
                              installedBy: data[0]['installedBy'],
                              auditPicS3Url: dataImage.isNotEmpty
                                  ? dataImage[0]['deviceImage']
                                  : '',
                              connection: data[0]['connection'],
                              poleCount: '',
                              specificUserPoleCount: 0,
                              defaultregion: dataBloc.region,
                              defaultzone: dataBloc.zone,
                              defaultward: dataBloc.ward,
                            )));
              }
            }
          });
        } else if (!validCharacters.hasMatch(trimmedContent)) {
          EasyLoading.dismiss(animation: true);
          alertPopUp1(
              context,
              'Pole number format is Invalid. Kindly scan a different QR code.',
              'assets/animation/warn.json');
        } else if (trimmedContent.length != 7 || trimmedContent.length != 8) {
          EasyLoading.dismiss(animation: true);
          alertPopUp1(
              context,
              'Pole number format is Invalid. Kindly scan a different QR code.',
              'assets/animation/warn.json');
        } else {
          EasyLoading.dismiss(animation: true);
        }
      }
    } else {
      await Permission.camera.request();
      PermissionStatus camera = await Permission.camera.status;
      if (camera == PermissionStatus.granted) {
        scanBarCode();
      } else if (camera == PermissionStatus.permanentlyDenied) {
        AppSettings.openAppSettings();
        scanBarCode();
      }
    }
  }
}
