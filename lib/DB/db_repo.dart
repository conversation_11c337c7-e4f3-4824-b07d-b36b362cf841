import 'package:schnell_pole_installation/DB/db.dart';
import 'package:schnell_pole_installation/DB/map_model.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseRepo {
  late DatabaseConnection _databaseConnection;
  DatabaseRepo() {
    _databaseConnection = DatabaseConnection();
  }
  static Database? _database;
  Future<Database> get databasedetails async {
    if (_database != null) {
      return _database!;
    } else {
      _database = await _databaseConnection.setDatabase();
      return _database!;
    }
  }

  // static Future<Database> db() async {
  //   return openDatabase(
  //     'cart.db',
  //     version: 3,
  //     onCreate: (Database database, int version) async {
  //       await _databaseConnection.imageDatabase();
  //     },
  //   );
  // }
  // Future<Database> get databaseImage async {
  //   if (_database != null) {
  //     print('database Already Created for Image');
  //     return _database!;
  //   } else {
  //     _database = await _databaseConnection.imageDatabase();
  //     print('New database Created for Image');
  //     return _database!;
  //   }
  // }

  insertImageData(table, PoleCountModel data) async {
    var connection = await databasedetails;
    return await connection.insert(table, data.imageMap());
  }

  readImageAllData(table) async {
    var connection = await databasedetails;
    return await connection.query(table);
  }

  insertDetailsData(table, PoleCountModel data) async {
    var connection = await databasedetails;
    return await connection.insert(table, data.userMap());
  }

  insertdayData(table, data) async {
    var connection = await databasedetails;
    return await connection.insert(
      table,
      data,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  insertPoleCountData(table, PoleCountModel data) async {
    var connection = await databasedetails;
    return await connection.insert(table, data.poleCountMap());
  }

  getPoleCount(table) async {
    var connection = await databasedetails;
    return await connection.query(table);
  }

  readOneDetailsData(table) async {
    var connection = await databasedetails;
    return await connection.query(
      table,
      limit: 1,
    );
  }

  readAllDataDetails(table) async {
    var connection = await databasedetails;
    return await connection.query(table, distinct: true);
  }

  readDetailsDatabyPoleName(table, polename) async {
    var connection = await databasedetails;
    return await connection
        .query(table, where: 'name=?', whereArgs: [polename]);
  }

  readDateDatabyPoleName(table, polename) async {
    var connection = await databasedetails;
    return await connection
        .query(table, where: 'date=?', whereArgs: [polename]);
  }

  readWardDatabyWardIdlen(table, wardId) async {
    var connection = await databasedetails;
    return await connection
        .query(table, where: 'wardId=?', whereArgs: [wardId]);
  }

  readWardDatabyWardId(table, wardId) async {
    var connection = await databasedetails;
    return await connection.rawQuery('''
      SELECT DISTINCT date,polecount 
      FROM $table  WHERE wardId = '$wardId' ORDER BY id
    ''');
  }

  increasePoleCountData(table, polecount, todayDate) async {
    var connection = await databasedetails;
    return await connection.update(
      table,
      {'polecount': polecount},
      where: 'date = ?',
      whereArgs: [todayDate],
    );
  }

  deleteTableDataByPoleName(table, polename) async {
    var connection = await databasedetails;
    return await connection
        .delete(table, where: 'name=?', whereArgs: [polename]);
  }

  deleteTableDetailsDatas(table) async {
    var connection = await databasedetails;
    return await connection.delete(table);
  }
}
