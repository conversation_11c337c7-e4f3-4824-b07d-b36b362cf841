import 'dart:async';
import 'dart:developer';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseConnection {
  Future<Database> setDatabase() async {
    var directory = await getApplicationDocumentsDirectory();
    var path = join(directory.path, "pole_db");
    String sql5 =
        "CREATE TABLE tbl_date_db (id INTEGER PRIMARY KEY AUTOINCREMENT,date TEXT,polecount INTEGER,wardId TEXT)";
    String sql4 =
        "CREATE TABLE tbl_duplicateimage_db (id INTEGER PRIMARY KEY AUTOINCREMENT,name TEXT,deviceImage TEXT,fileName TEXT,latitude DOUBLE,longitude DOUBLE,accuracy DOUBLE,location TEXT,manualEnteredLocation TEXT,wardName TEXT,zoneName TEXT,region TEXT)";
    String sql3 =
        "CREATE TABLE tbl_polecount_db (id INTEGER PRIMARY KEY AUTOINCREMENT,name TEXT,wardId TEXT)";
    String sql2 =
        "CREATE TABLE tbl_duplicate_db (id INTEGER PRIMARY KEY AUTOINCREMENT,name TEXT,type TEXT,customerId TEXT,wardId TEXT,latitude DOUBLE,longitude DOUBLE,accuracy DOUBLE,location TEXT,manualEnteredLocation TEXT,state TEXT,wardName TEXT,zoneName TEXT,region TEXT,installedOn INTEGER,installedBy TEXT,clampDimension TEXT,lampProfiles TEXT,connection TEXT,armCount INTEGER,uuidFileName TEXT)";
    String sql1 =
        "CREATE TABLE tbl_image_db (id INTEGER PRIMARY KEY AUTOINCREMENT,name TEXT,deviceImage TEXT,fileName TEXT,latitude DOUBLE,longitude DOUBLE,accuracy DOUBLE,location TEXT,manualEnteredLocation TEXT,wardName TEXT,zoneName TEXT,region TEXT)";
    String sql =
        "CREATE TABLE tbl_pole_db (id INTEGER PRIMARY KEY AUTOINCREMENT,name TEXT,type TEXT,customerId TEXT,wardId TEXT,latitude DOUBLE,longitude DOUBLE,accuracy DOUBLE,location TEXT,manualEnteredLocation TEXT,state TEXT,wardName TEXT,zoneName TEXT,region TEXT,installedOn INTEGER,installedBy TEXT,clampDimension TEXT,lampProfiles TEXT,connection TEXT,armCount INTEGER,uuidFileName TEXT)";
    var database = await openDatabase(
      path,
      version: 2,
      onCreate: (db, version) {
        db.execute(sql);
        log("sql database created");
        db.execute(sql1);
        db.execute(sql2);
        db.execute(sql3);
        db.execute(sql4);
        return db.execute(sql5);
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (oldVersion < newVersion) {
          log(oldVersion.toString());
          log(newVersion.toString());
          await _addColumnIfNotExists(db, 'tbl_image_db', 'latitude', 'DOUBLE');
          await _addColumnIfNotExists(
              db, 'tbl_image_db', 'longitude', 'DOUBLE');
          await _addColumnIfNotExists(db, 'tbl_image_db', 'accuracy', 'DOUBLE');
          await _addColumnIfNotExists(db, 'tbl_image_db', 'location', 'TEXT');
          await _addColumnIfNotExists(
              db, 'tbl_image_db', 'manualEnteredLocation', 'TEXT');
          await _addColumnIfNotExists(db, 'tbl_image_db', 'wardName', 'TEXT');
          await _addColumnIfNotExists(db, 'tbl_image_db', 'zoneName', 'TEXT');
          await _addColumnIfNotExists(db, 'tbl_image_db', 'region', 'TEXT');

          // Check and add columns for tbl_duplicateimage_db
          await _addColumnIfNotExists(
              db, 'tbl_duplicateimage_db', 'latitude', 'DOUBLE');
          await _addColumnIfNotExists(
              db, 'tbl_duplicateimage_db', 'longitude', 'DOUBLE');
          await _addColumnIfNotExists(
              db, 'tbl_duplicateimage_db', 'accuracy', 'DOUBLE');
          await _addColumnIfNotExists(
              db, 'tbl_duplicateimage_db', 'location', 'TEXT');
          await _addColumnIfNotExists(
              db, 'tbl_duplicateimage_db', 'manualEnteredLocation', 'TEXT');
          await _addColumnIfNotExists(
              db, 'tbl_duplicateimage_db', 'wardName', 'TEXT');
          await _addColumnIfNotExists(
              db, 'tbl_duplicateimage_db', 'zoneName', 'TEXT');
          await _addColumnIfNotExists(
              db, 'tbl_duplicateimage_db', 'region', 'TEXT');

          // Fetch records from tbl_image_db
          List<Map<String, dynamic>> imageRecords =
              await db.query('tbl_image_db');

          // Iterate through each record and update latitude and longitude if null
          for (var record in imageRecords) {
            if (record['latitude'] == null || record['longitude'] == null) {
              String name = record['name'];

              // Fetch corresponding pole record
              List<Map<String, dynamic>> poleRecords = await db
                  .query('tbl_pole_db', where: 'name = ?', whereArgs: [name]);

              if (poleRecords.isNotEmpty) {
                var poleRecord = poleRecords.first;
                log('${poleRecord['name']},${poleRecord['latitude']},${poleRecord['longitude']}');
                // Update latitude and longitude from pole record
                await db.update(
                    'tbl_image_db',
                    {
                      'latitude': poleRecord['latitude'],
                      'longitude': poleRecord['longitude'],
                      'accuracy': poleRecord['accuracy'],
                      'location': poleRecord['location'],
                      'manualEnteredLocation':
                          poleRecord['manualEnteredLocation'],
                      'wardName': poleRecord['wardName'],
                      'zoneName': poleRecord['zoneName'],
                      'region': poleRecord['region'],
                    },
                    where: 'name = ?',
                    whereArgs: [poleRecord['name']]);
              }
            }
          }
        }
      },
    );
    return database;
  }

  Future<void> _addColumnIfNotExists(Database db, String tableName,
      String columnName, String columnType) async {
    var result = await db.rawQuery("PRAGMA table_info($tableName)");
    var columnExists = result.any((element) => element['name'] == columnName);

    if (!columnExists) {
      await db
          .execute("ALTER TABLE $tableName ADD COLUMN $columnName $columnType");
      log("Added column $columnName to $tableName");
    }
  }
}
