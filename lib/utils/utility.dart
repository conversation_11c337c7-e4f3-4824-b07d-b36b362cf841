import 'dart:developer';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';

class Utility {
  static Future<bool> isConnected() async {
    var connectivityResult = await (Connectivity().checkConnectivity());
    log(connectivityResult.toString());
    if (connectivityResult.contains(ConnectivityResult.mobile)) {
      try {
        final result = await InternetAddress.lookup('iotpro.io');
        return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      } on SocketException catch (_) {
        return false;
      }
    } else if (connectivityResult.contains(ConnectivityResult.wifi)) {
      try {
        final result = await InternetAddress.lookup('iotpro.io');
        return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      } on SocketException catch (_) {
        return false;
      }
    }
    return false;
  }
}
