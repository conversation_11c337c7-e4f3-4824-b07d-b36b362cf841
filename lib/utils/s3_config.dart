import '../env.dart';

class S3Config {
  static String get bucketName {
    switch (currentEnvironment) {
      case Environment.development:
        return 'luminator-iotpro';
      case Environment.production:
      case Environment.staging:
        return 'schnell-s3-image';
    }
  }

  static String get region {
    switch (currentEnvironment) {
      case Environment.development:
        return 'us-east-1';
      case Environment.production:
      case Environment.staging:
        return 'ap-south-1';
    }
  }

  static String get accessKey {
    switch (currentEnvironment) {
      case Environment.development:
        return '********************';
      case Environment.production:
      case Environment.staging:
        return '********************';
    }
  }

  static String get secretKey {
    switch (currentEnvironment) {
      case Environment.development:
        return 'wLtFR5vFUs1q0xfpWs8JaqTPVefxhvNnPl9E04uR';
      case Environment.production:
      case Environment.staging:
        return 't99dd9UHF3h4OVvxbosN+JnJIsaD4jd/d+7JNIER';
    }
  }

  static const String luminatorFolder = 'luminator';
  static const String ppeImageFolder = 'ppeImages';
  static const String grievanceFolder = 'grievances';

  static String get s3Endpoint =>
      'https://$bucketName.s3.$region.amazonaws.com';
}
