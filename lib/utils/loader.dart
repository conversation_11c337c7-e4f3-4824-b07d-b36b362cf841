import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:lottie/lottie.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
// import 'package:quickalert/models/quickalert_type.dart';
// import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:schnell_pole_installation/utils/constants.dart';

loaderAnimation(string) {
  EasyLoading.instance
    ..indicatorType = EasyLoadingIndicatorType.ring
    ..loadingStyle = EasyLoadingStyle.light
    ..indicatorColor = Colors.black
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..textColor = Colors.black
    ..backgroundColor = Colors.transparent
    ..userInteractions = false
    ..dismissOnTap = false
    ..maskColor = Colors.white.withOpacity(0.5)
    ..boxShadow = <BoxShadow>[];
  EasyLoading.show(status: string, maskType: EasyLoadingMaskType.none);
}

snackBarLoader(string, context) {
  ScaffoldMessenger.of(context).showSnackBar(SnackBar(
    content: Text(string),
    duration: const Duration(seconds: 2),
  ));
}

// Future<String> internetsnackBar(context, title, msg) async {
//   var value;
//   await Flushbar(
//       backgroundColor: const Color.fromARGB(255, 206, 68, 68),
//       title: title,
//       message: msg,
//       icon: const Icon(
//         Icons.cancel_outlined,
//         size: 30,
//         color: Colors.white,
//       ),
//       isDismissible: false,
//       blockBackgroundInteraction: true,
//       leftBarIndicatorColor: Colors.white,
//       onTap: (flushbar) {
//         value = '1';
//         flushbar.dismiss();
//       }
//       // duration: Duration(seconds: 20),
//       ).show(context);
//   return value;
// }

Future<String> forceAlert(context, value) async {
  var value1;
  var result = await QuickAlert.show(
    context: context,
    type: QuickAlertType.info,
    showConfirmBtn: true,
    title: 'Please Download The App From PlayStore!',
    // textTextStyle: const TextStyle(color: Colors.black),
    text: 'Click to Continue.',
    // lottieAsset: value,
    backgroundColor: const Color.fromARGB(255, 255, 255, 255),
    confirmBtnColor: darkBlue,
    onConfirmBtnTap: () => {
      value1 = '0',
    },
  );
  return value1;
}

alertPopUpTextBold(context, msg, type) => QuickAlert.show(
      context: context,
      type: QuickAlertType.loading,
      showCancelBtn: false,
      width: 500,
      text: msg,
      // textTextStyle:
      //     const TextStyle(fontWeight: FontWeight.bold, color: Colors.black),
      // lottieAsset: type,
      autoCloseDuration: const Duration(seconds: 3),
    );

alertPopUp(context, msg, type) => QuickAlert.show(
      context: context,
      type: QuickAlertType.loading,
      showCancelBtn: false,
      width: 500,
      text: msg,
      // lottieAsset: type,
      // textTextStyle: const TextStyle(color: Colors.black),
      autoCloseDuration: const Duration(seconds: 3),
    );
alertPopUp1(context, msg, type) => QuickAlert.show(
      context: context,
      type: QuickAlertType.loading,
      showCancelBtn: false,
      // textTextStyle: const TextStyle(color: Colors.black),
      width: 500,
      text: msg,
      // lottieAsset: type,
      autoCloseDuration: const Duration(seconds: 5),
    );
alertPopUpConstant(context, msg, type) => QuickAlert.show(
      context: context,
      type: QuickAlertType.loading,
      showCancelBtn: false,
      width: 500,
      text: msg,
      // textTextStyle: const TextStyle(color: Colors.black),
      // lottieAsset: type,
      barrierDismissible: false,
    );
connectivityLoaderAnimation(string) {
  EasyLoading.instance
    ..indicatorType = EasyLoadingIndicatorType.fadingCircle
    ..loadingStyle = EasyLoadingStyle.custom
    ..indicatorColor = Colors.black
    ..indicatorSize = 60.0
    ..radius = 10.0
    ..textColor = darkBlue
    ..textStyle = const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)
    ..backgroundColor = Colors.transparent
    ..userInteractions = false
    ..dismissOnTap = false
    ..maskColor = Colors.white.withOpacity(0.5)
    ..boxShadow = <BoxShadow>[];
  EasyLoading.show(
    indicator: LottieBuilder.asset(
      string,
    ),
    status: 'You seem Offline! Please check your connection.',
    maskType: EasyLoadingMaskType.custom,
  );
}

Future<bool> showExitPopup(context) async {
  var bg = const Color.fromARGB(255, 200, 220, 236);
  return await showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: bg,
          content: Container(
            height: 104,
            color: bg,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "Are you sure you want to exit?",
                  style: TextStyle(color: Colors.black),
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          exit(0);
                        },
                        style: ElevatedButton.styleFrom(
                            backgroundColor:
                                const Color.fromARGB(255, 234, 34, 19)),
                        child: const Text(
                          "Yes",
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                    const SizedBox(width: 15),
                    Expanded(
                        child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.black,
                      ),
                      child: const Text("No",
                          style: TextStyle(color: Colors.white)),
                    ))
                  ],
                )
              ],
            ),
          ),
        );
      });
}
