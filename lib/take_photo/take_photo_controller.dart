import 'dart:developer';
import 'package:schnell_pole_installation/DB/db_repo.dart';
import 'package:schnell_pole_installation/DB/map_model.dart';
import 'package:schnell_pole_installation/take_photo/take_photo_service.dart';

import '../survey/s3_upload_service.dart';

class TakePhotoController {
  final TakePhotoService _service = TakePhotoService();
  final S3UploadService _s3UploadService = S3UploadService();

  imageUpdationS3(deviceImage, token, imageData, context) async {
    var result = await _s3UploadService.updateSurveyImageServiceS3(
        deviceImage, imageData['fileName'], context);

    if (result == "200") {
      log('${imageData['name']} image uploaded successfully');
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_image_db', imageData['name']);
    } else if (result == "400") {
      var poleCountData = PoleCountModel();
      poleCountData.deviceImage = deviceImage;
      poleCountData.imagePoleName = imageData['name'];
      poleCountData.imageFileName = imageData['fileName'];
      poleCountData.wtrmrkLatitude = imageData['latitude'];
      poleCountData.wtrmrkLongitude = imageData['longitude'];
      poleCountData.wtrmrkAccuracy = imageData['accuracy'];
      poleCountData.wtrmrkManualEnteredLocation =
          imageData['manualEnteredLocation'];
      poleCountData.wtrmrkLocation = imageData['location'];
      poleCountData.wtrmrkRegion = imageData['region'];
      poleCountData.wtrmrkZone = imageData['zoneName'];
      poleCountData.wtrmrkWard = imageData['wardName'];
      var duplicateDbData = await DatabaseRepo()
          .insertImageData('tbl_duplicateimage_db', poleCountData);
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_image_db', imageData['name']);
    }
  }

  imageUpdation(deviceImage, token, imageData, context) async {
    var result = await _s3UploadService.updateSurveyImageServiceS3(
        deviceImage, imageData['fileName'], context);
    if (result == "200") {
      log('${imageData['name']} image uploaded successfully');
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_image_db', imageData['name']);
    } else if (result == "400") {
      var poleCountData = PoleCountModel();
      poleCountData.deviceImage = deviceImage;
      poleCountData.imagePoleName = imageData['name'];
      poleCountData.imageFileName = imageData['fileName'];
      poleCountData.wtrmrkLatitude = imageData['latitude'];
      poleCountData.wtrmrkLongitude = imageData['longitude'];
      poleCountData.wtrmrkAccuracy = imageData['accuracy'];
      poleCountData.wtrmrkManualEnteredLocation =
          imageData['manualEnteredLocation'];
      poleCountData.wtrmrkLocation = imageData['location'];
      poleCountData.wtrmrkRegion = imageData['region'];
      poleCountData.wtrmrkZone = imageData['zoneName'];
      poleCountData.wtrmrkWard = imageData['wardName'];
      var duplicateDbData = await DatabaseRepo()
          .insertImageData('tbl_duplicateimage_db', poleCountData);
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_image_db', imageData['name']);
    }
  }
}
