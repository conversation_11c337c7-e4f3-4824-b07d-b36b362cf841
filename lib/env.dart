enum Environment {
  development,
  staging,
  production,
}

Environment get currentEnvironment {
  const flavor = String.fromEnvironment('flavor');
  switch (flavor) {
    case 'development':
      return Environment.development;
    case 'staging':
      return Environment.staging;
    case 'production':
    default:
      return Environment.production;
  }
}

String get baseUrl {
  switch (currentEnvironment) {
    case Environment.development:
      return 'https://api.iotpro.io';
    case Environment.staging:
      return 'https://beta.api.schnelliot.in';
    case Environment.production:
      return 'https://api.schnelliot.in';
  }
}

String get ticketUrl {
  switch (currentEnvironment) {
    case Environment.development:
      return 'https://iotpro.io';
    case Environment.production:
    case Environment.staging:
      return 'https://schnelliot.in';
  }
}
