import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:schnell_pole_installation/Pole_Details/pole_display_details.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/model_pole_installation.dart';
import 'package:schnell_pole_installation/Pole_Installation_Page/pole_installation_service.dart';
import 'package:schnell_pole_installation/utils/loader.dart';

class PoleInstallationController {
  final PoleInstallationService _service = PoleInstallationService();
  PoleInstallationModel? data;
  var ass = 'pole';
  validatePoll(value, context, token, mode, poleCount, specificUserPoleCount,
      defaultRegion, defaultzone, defaultWard) async {
    var result = await _service.pollInstall(
        value, context, token, mode, poleCount, specificUserPoleCount);
    EasyLoading.dismiss(animation: true);
    if (result == '1') {
      if (poleDetailsData!.assetType == 'pole') {
        EasyLoading.dismiss(animation: true);
        if (mode == '1') {
          Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                  builder: (context) => PoleDisplayDetails(
                        token: token,
                        region: poleDetailsData!.region,
                        zone: poleDetailsData!.zoneName,
                        ward: poleDetailsData!.wardName,
                        poleNumber: poleDetailsData!.name,
                        clampDimension: poleDetailsData!.clampDimension,
                        poleType: poleDetailsData!.type,
                        noOfArms: poleDetailsData!.armCount,
                        lampProfiles: poleDetailsData!.lampProfiles,
                        // lampType: poleDetailsData!.lampType,
                        // lampWattage: poleDetailsData!.lampWattage,
                        customerId: poleDetailsData!.id,
                        wardId: poleDetailsData!.id,
                        latitude: poleDetailsData!.latitude,
                        longitude: poleDetailsData!.longitude,
                        accuracy: poleDetailsData!.accuracy,
                        location: poleDetailsData!.location,
                        state: poleDetailsData!.state,
                        installedOn: poleDetailsData!.installedOn,
                        installedBy: poleDetailsData!.installedBy,
                        auditPicS3Url: poleDetailsData!.auditPicS3Url,
                        connection: poleDetailsData!.connection,
                        poleCount: poleCount,
                        specificUserPoleCount: specificUserPoleCount,
                        defaultregion: defaultRegion,
                        defaultzone: defaultzone,
                        defaultward: defaultWard,
                      )));
        } else {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => PoleDisplayDetails(
                        token: token,
                        region: poleDetailsData!.region,
                        zone: poleDetailsData!.zoneName,
                        ward: poleDetailsData!.wardName,
                        poleNumber: poleDetailsData!.name,
                        clampDimension: poleDetailsData!.clampDimension,
                        poleType: poleDetailsData!.type,
                        noOfArms: poleDetailsData!.armCount,
                        lampProfiles: poleDetailsData!.lampProfiles,
                        // lampType: poleDetailsData!.lampType,
                        // lampWattage: poleDetailsData!.lampWattage,
                        customerId: poleDetailsData!.id,
                        wardId: poleDetailsData!.id,
                        latitude: poleDetailsData!.latitude,
                        longitude: poleDetailsData!.longitude,
                        accuracy: poleDetailsData!.accuracy,
                        location: poleDetailsData!.location,
                        state: poleDetailsData!.state,
                        installedOn: poleDetailsData!.installedOn,
                        installedBy: poleDetailsData!.installedBy,
                        auditPicS3Url: poleDetailsData!.auditPicS3Url,
                        connection: poleDetailsData!.connection,
                        poleCount: poleCount,
                        specificUserPoleCount: specificUserPoleCount,
                        defaultregion: defaultRegion,
                        defaultzone: defaultzone,
                        defaultward: defaultWard,
                      )));
        }
      } else {
        EasyLoading.dismiss(animation: true);
        alertPopUp1(
            context,
            'An asset of type ${poleDetailsData!.assetType} is already profiled for ${poleDetailsData!.name}. You cannot perform Pole installation/maintenance activities on ${poleDetailsData!.name}',
            'assets/animation/warn.json');
      }
    }
  }
}
