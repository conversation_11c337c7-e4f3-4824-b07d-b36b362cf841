class PoleInstallationModel {
  String id;
  String name;
  String type;
  String latitude;
  String longitude;
  String accuracy;
  String location;
  String wardName;
  String zoneName;
  String state;
  String region;
  String installedOn;
  String installedBy;
  String clampDimension;
  String lampProfiles;
  // String lampType;
  // String lampWattage;
  String connection;
  String armCount;
  String auditPicS3Url;
  String assetType;
  PoleInstallationModel({
    required this.type,
    required this.id,
    required this.name,
    required this.accuracy,
    required this.latitude,
    required this.longitude,
    required this.location,
    required this.wardName,
    required this.zoneName,
    required this.state,
    required this.region,
    required this.installedOn,
    required this.installedBy,
    required this.clampDimension,
    required this.lampProfiles,
    // required this.lampType,
    // required this.lampWattage,
    required this.connection,
    required this.armCount,
    required this.assetType,
    required this.auditPicS3Url,
  });

  factory PoleInstallationModel.fromjson(Map<String, dynamic> json) {
    return PoleInstallationModel(
      id: json["id"],
      name: json["name"],
      type: json["pole_type"],
      latitude: json["latitude"],
      longitude: json["longitude"],
      accuracy: json["accuracy"] ?? '',
      location: json['location'],
      wardName: json['wardName'],
      zoneName: json['zoneName'],
      state: json['state'],
      region: json['region'],
      installedOn: json['installedOn'],
      installedBy: json['installedBy'],
      clampDimension: json['clampDimension'],
      lampProfiles: json['lampProfiles'] ?? '',
      connection: json['connection'],
      armCount: json['armCount'],
      assetType: json['asset_type'],
      auditPicS3Url: json['auditPicS3Url'],
    );
  }
}
