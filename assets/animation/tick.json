{"v": "5.6.7", "fr": 60, "ip": 0, "op": 240, "w": 1200, "h": 1200, "nm": "Comp 1", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "check", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [575.25, 603.465, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 114, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 130, "s": [105, 105, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 136, "s": [95, 95, 100]}, {"t": 142, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Excite - Skew - Transform", "np": 8, "mn": "Pseudo/BNCA2506f0b33", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/BNCA2506f0b33-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/BNCA2506f0b33-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "Overshoot", "mn": "Pseudo/BNCA2506f0b33-0003", "ix": 3, "v": {"a": 0, "k": 10, "ix": 3, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/BNCA2506f0b33-0004", "ix": 4, "v": {"a": 0, "k": 15, "ix": 4, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 0, "nm": "Friction", "mn": "Pseudo/BNCA2506f0b33-0005", "ix": 5, "v": {"a": 0, "k": 40, "ix": 5, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 6, "nm": "", "mn": "Pseudo/BNCA2506f0b33-0006", "ix": 6, "v": 0}]}, {"ty": 5, "nm": "Excite - Scale - Transform", "np": 8, "mn": "Pseudo/BNCA2506f0b33", "ix": 2, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/BNCA2506f0b33-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/BNCA2506f0b33-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "Overshoot", "mn": "Pseudo/BNCA2506f0b33-0003", "ix": 3, "v": {"a": 0, "k": 10, "ix": 3, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/BNCA2506f0b33-0004", "ix": 4, "v": {"a": 0, "k": 15, "ix": 4, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 0, "nm": "Friction", "mn": "Pseudo/BNCA2506f0b33-0005", "ix": 5, "v": {"a": 0, "k": 40, "ix": 5, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 6, "nm": "", "mn": "Pseudo/BNCA2506f0b33-0006", "ix": 6, "v": 0}]}, {"ty": 5, "nm": "Excite - Position - Transform", "np": 8, "mn": "Pseudo/BNCA2506f0b33", "ix": 3, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/BNCA2506f0b33-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/BNCA2506f0b33-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "Overshoot", "mn": "Pseudo/BNCA2506f0b33-0003", "ix": 3, "v": {"a": 0, "k": 10, "ix": 3, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/BNCA2506f0b33-0004", "ix": 4, "v": {"a": 0, "k": 15, "ix": 4, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 0, "nm": "Friction", "mn": "Pseudo/BNCA2506f0b33-0005", "ix": 5, "v": {"a": 0, "k": 40, "ix": 5, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 6, "nm": "", "mn": "Pseudo/BNCA2506f0b33-0006", "ix": 6, "v": 0}]}, {"ty": 5, "nm": "Excite - Anchor Point - Transform", "np": 8, "mn": "Pseudo/BNCA2506f0b33", "ix": 4, "en": 1, "ef": [{"ty": 7, "nm": "Enable", "mn": "Pseudo/BNCA2506f0b33-0001", "ix": 1, "v": {"a": 0, "k": 1, "ix": 1}}, {"ty": 6, "nm": "Properties", "mn": "Pseudo/BNCA2506f0b33-0002", "ix": 2, "v": 0}, {"ty": 0, "nm": "Overshoot", "mn": "Pseudo/BNCA2506f0b33-0003", "ix": 3, "v": {"a": 0, "k": 10, "ix": 3, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 0, "nm": "<PERSON><PERSON><PERSON>", "mn": "Pseudo/BNCA2506f0b33-0004", "ix": 4, "v": {"a": 0, "k": 15, "ix": 4, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 0, "nm": "Friction", "mn": "Pseudo/BNCA2506f0b33-0005", "ix": 5, "v": {"a": 0, "k": 40, "ix": 5, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}, {"ty": 6, "nm": "", "mn": "Pseudo/BNCA2506f0b33-0006", "ix": 6, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[214, -137], [-68, 130], [-164, 34]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 56, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar enable, amp, freq, decay, n, t, v;\ntry {\n    $bm_rt = enable = effect('Excite - Position - Transform')('Pseudo/BNCA2506f0b33-0001');\n    if (enable == 0) {\n        $bm_rt = value;\n    } else {\n        amp = $bm_div(effect('Excite - Position - Transform')('Pseudo/BNCA2506f0b33-0003'), 2.5);\n        freq = $bm_div(effect('Excite - Position - Transform')('Pseudo/BNCA2506f0b33-0004'), 20);\n        decay = $bm_div(effect('Excite - Position - Transform')('Pseudo/BNCA2506f0b33-0005'), 20);\n        n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n    }\n} catch (err) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar enable, amp, freq, decay, n, t, v;\ntry {\n    $bm_rt = enable = effect('Excite - Anchor Point - Transform')('Pseudo/BNCA2506f0b33-0001');\n    if (enable == 0) {\n        $bm_rt = value;\n    } else {\n        amp = $bm_div(effect('Excite - Anchor Point - Transform')('Pseudo/BNCA2506f0b33-0003'), 2.5);\n        freq = $bm_div(effect('Excite - Anchor Point - Transform')('Pseudo/BNCA2506f0b33-0004'), 20);\n        decay = $bm_div(effect('Excite - Anchor Point - Transform')('Pseudo/BNCA2506f0b33-0005'), 20);\n        n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n    }\n} catch (err) {\n    $bm_rt = value = value;\n}"}, "s": {"a": 0, "k": [99, 99], "ix": 3, "x": "var $bm_rt;\nvar enable, amp, freq, decay, n, t, v;\ntry {\n    $bm_rt = enable = effect('Excite - Scale - Transform')('Pseudo/BNCA2506f0b33-0001');\n    if (enable == 0) {\n        $bm_rt = value;\n    } else {\n        amp = $bm_div(effect('Excite - Scale - Transform')('Pseudo/BNCA2506f0b33-0003'), 2.5);\n        freq = $bm_div(effect('Excite - Scale - Transform')('Pseudo/BNCA2506f0b33-0004'), 20);\n        decay = $bm_div(effect('Excite - Scale - Transform')('Pseudo/BNCA2506f0b33-0005'), 20);\n        n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n    }\n} catch (err) {\n    $bm_rt = value = value;\n}"}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar enable, amp, freq, decay, n, t, v;\ntry {\n    $bm_rt = enable = effect('Excite - Skew - Transform')('Pseudo/BNCA2506f0b33-0001');\n    if (enable == 0) {\n        $bm_rt = value;\n    } else {\n        amp = $bm_div(effect('Excite - Skew - Transform')('Pseudo/BNCA2506f0b33-0003'), 2.5);\n        freq = $bm_div(effect('Excite - Skew - Transform')('Pseudo/BNCA2506f0b33-0004'), 20);\n        decay = $bm_div(effect('Excite - Skew - Transform')('Pseudo/BNCA2506f0b33-0005'), 20);\n        n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : $bm_sub(time, key(n).time), $bm_rt = 0 < n ? (v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10))), $bm_sum(value, $bm_div($bm_mul($bm_mul($bm_div(v, 100), amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))))) : value;\n    }\n} catch (err) {\n    $bm_rt = value = value;\n}"}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 42, "s": [100]}, {"t": 58, "s": [0]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "circle - stroke", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 35, "s": [100]}, {"t": 36, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [617.721, 614.58, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[172.277, 0], [0, -172.277], [-172.277, 0], [0, 172.277]], "o": [[-172.277, 0], [0, 172.277], [172.277, 0], [0, -172.277]], "v": [[0, -311.936], [-311.936, 0], [0, 311.936], [311.936, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.10588235294117647, 0.7254901960784313, 0.20392156862745098, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 40, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-17.721, -14.58], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [130.536, 130.536], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 0, "s": [100]}, {"t": 23, "s": [0]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 2, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "circle - bg", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [617.585, 614.469, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.5, 0.5, 0.5], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 19, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.5, 0.5, 0.5], "y": [0, 0, 0]}, "t": 33, "s": [114.99999999999999, 114.99999999999999, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 40, "s": [95, 95, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 45, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 114, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 130, "s": [110.00000000000001, 110.00000000000001, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 136, "s": [95, 95, 100]}, {"t": 142, "s": [105, 105, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[172.277, 0], [0, -172.277], [-172.277, 0], [0, 172.277]], "o": [[-172.277, 0], [0, 172.277], [172.277, 0], [0, -172.277]], "v": [[0, -311.936], [-311.936, 0], [0, 311.936], [311.936, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.10588235294117647, 0.7254901960784313, 0.20392156862745098, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-17.721, -14.58], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [130.536, 130.536], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "burst", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [612.759, 610.498, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [72, 72, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[172.277, 0], [0, -172.277], [-172.277, 0], [0, 172.277]], "o": [[-172.277, 0], [0, 172.277], [172.277, 0], [0, -172.277]], "v": [[0, -311.936], [-311.936, 0], [0, 311.936], [311.936, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.10588235294117647, 0.7254901960784313, 0.20392156862745098, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 30, "s": [40]}, {"t": 68, "s": [0]}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 0, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 0, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 0, "k": 0, "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-17.721, -14.58], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.5, 0.5], "y": [1, 1]}, "o": {"x": [0.5, 0.5], "y": [0, 0]}, "t": 26, "s": [167.536, 167.536]}, {"t": 68, "s": [251.536, 251.536]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 26, "s": [0]}, {"t": 34, "s": [100]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "burst 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [612.759, 610.498, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [72, 72, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[172.277, 0], [0, -172.277], [-172.277, 0], [0, 172.277]], "o": [[-172.277, 0], [0, 172.277], [172.277, 0], [0, -172.277]], "v": [[0, -311.936], [-311.936, 0], [0, 311.936], [311.936, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.10588235294117647, 0.7254901960784313, 0.20392156862745098, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 1, "k": [{"i": {"x": [0.5], "y": [1]}, "o": {"x": [0.5], "y": [0]}, "t": 122, "s": [40]}, {"t": 170, "s": [0]}], "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 0, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 0, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 0, "k": 0, "ix": 7}}], "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-17.721, -14.58], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.5, 0.5], "y": [1, 1]}, "o": {"x": [0.5, 0.5], "y": [0, 0]}, "t": 118, "s": [167.536, 167.536]}, {"t": 170, "s": [251.536, 251.536]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 124, "s": [0]}, {"t": 132, "s": [100]}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}], "markers": []}